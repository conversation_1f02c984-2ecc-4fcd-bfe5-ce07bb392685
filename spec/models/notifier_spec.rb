require 'rails_helper'

RSpec.describe Notifier, type: :model do
  describe 'association' do
    it 'should belongs_to notification_template' do
      notification_template_association = Notifier.reflect_on_association(:notification_template)
      expect(notification_template_association.macro).to eq(:belongs_to)
    end
  end

  describe 'validation' do
    let(:notifier){ build(:notifier) }

    it 'should validate recipient_type' do
      notifier.recipient_type = 'deal'
      expect(notifier).to be_invalid
      expect(notifier.errors[:recipient_type]).to include("deal is not a valid recipient type")
    end
  end

  describe '#get_team_or_chat_id' do
    let(:tenant){ create(:tenant) }
    let(:connected_account){ create(:connected_account, tenant_id: tenant.id) }
    let(:notification_template){ create(:notification_template, tenant_id: tenant.id, connected_account_id: connected_account.id) }
    let(:notifier){ create(:notifier, notification_template_id: notification_template.id) }
    let(:agent_mapping){ create(:agent_mapping, user_id: connected_account.user_id, tenant_id: tenant.id, connected_account_id: connected_account.id) }

    context 'when failed to get team_or_chat_id' do
      context 'when recipient_type is channel' do
        it 'should return nil if team_or_chat_id is nil' do
          notifier.team_or_chat_id = nil
          expect(notifier.get_team_or_chat_id).to be_nil
        end
      end

      context 'when recipient_type is user' do
        before { notifier.recipient_type = USER }

        it 'should return nil if access token of user is expired' do
          notifier.team_or_chat_id = nil

          expect_any_instance_of(Teams::FetchUsersService)
            .to receive(:get_chat_id)
            .and_return({ success: false })

          expect(notifier.get_team_or_chat_id).to be_nil
        end
      end

      context 'when recipient_type is relative' do
        before { notifier.recipient_type = RELATIVE }

        context 'when recipient_id is record_owner' do
          let(:another_user){ create(:user, tenant_id: tenant.id, email: '<EMAIL>') }
          before { notifier.recipient_id = 'record_owner' }

          context 'when user not found in database' do
            it 'returns nil' do
              expect(notifier.get_team_or_chat_id(record_owner: 123, tenant_id: connected_account.tenant_id)).to be_nil
            end
          end

          context 'when user found in database' do
            before { agent_mapping.update_column(:user_id, another_user.id) }

            context 'when agent mapping not found in database' do
              it 'returns nil' do
                expect(notifier.get_team_or_chat_id(record_owner: connected_account.user.kylas_user_id, tenant_id: connected_account.tenant_id)).to be_nil
              end
            end

            context 'when agent mapping found in database' do
              before do
                agent_mapping.update({ user_id: connected_account.user_id, tenant_id: connected_account.user.tenant_id })
                expect_any_instance_of(Teams::FetchUsersService)
                  .to receive(:get_chat_id)
                  .and_return({ success: false })
              end

              context 'when failed to fetch the user team_or_chat id' do
                it 'returns nil' do
                  expect(notifier.get_team_or_chat_id(record_owner: connected_account.user.kylas_user_id, tenant_id: connected_account.user.tenant_id)).to be_nil
                end
              end
            end
          end
        end

        context 'when recipient_id is record_created_by' do
          let(:another_user){ create(:user, tenant_id: tenant.id, email: '<EMAIL>') }
          before { notifier.recipient_id = 'record_created_by' }

          context 'when user not found in database' do
            it 'returns nil' do
              expect(notifier.get_team_or_chat_id(record_created_by: 123, tenant_id: connected_account.tenant_id)).to be_nil
            end
          end

          context 'when user found in database' do
            before do
              agent_mapping.update_column(:user_id, another_user.id)
            end

            context 'when agent mapping not found in database' do
              it 'returns nil' do
                expect(notifier.get_team_or_chat_id(record_created_by: connected_account.user.kylas_user_id, tenant_id: connected_account.tenant_id)).to be_nil
              end
            end

            context 'when agent mapping found in database' do
              before do
                notifier.recipient_id = 'record_created_by'
                agent_mapping.update_column(:user_id, connected_account.user_id)
                agent_mapping.update_column(:tenant_id, connected_account.user.tenant_id)
                expect_any_instance_of(Teams::FetchUsersService)
                  .to receive(:get_chat_id)
                  .and_return({ success: false })
              end

              context 'when failed to fetch the user team_or_chat id' do
                it 'returns nil' do
                  expect(notifier.get_team_or_chat_id(record_created_by: connected_account.user.kylas_user_id, tenant_id: connected_account.user.tenant_id)).to be_nil
                end
              end
            end
          end
        end

        context 'when recipient_id is record_updated_by' do
          let(:another_user){ create(:user, tenant_id: tenant.id, email: '<EMAIL>') }

          context 'when user not found in database' do
            it 'returns nil' do
              expect(notifier.get_team_or_chat_id(record_updated_by: 123, tenant_id: connected_account.tenant_id)).to be_nil
            end
          end

          context 'when user found in database' do
            before do
              agent_mapping.update_column(:user_id, another_user.id)
            end

            context 'when agent mapping not found in database' do
              it 'returns nil' do
                expect(notifier.get_team_or_chat_id(record_updated_by: connected_account.user.kylas_user_id, tenant_id: connected_account.tenant_id)).to be_nil
              end
            end

            context 'when agent mapping found in database' do
              before do
                agent_mapping.update({ user_id: connected_account.user_id, tenant_id: connected_account.user.tenant_id })
              end

              context 'when failed to fetch the user team_or_chat id' do
                it 'returns nil' do
                  expect(notifier.get_team_or_chat_id(record_updated_by: connected_account.user.kylas_user_id, tenant_id: connected_account.user.tenant_id)).to be_nil
                end
              end
            end
          end
        end
      end
    end

    context 'when successfully get team_or_chat_id' do
      context 'when recipient_type is channel' do
        it 'should return the stored team_or_chat_id' do
          expect(notifier.get_team_or_chat_id).to eq(notifier.team_or_chat_id)
        end
      end

      context 'when recipient_type is user' do
        before(:each) { notifier.recipient_type = USER }

        context 'when team_or_chat_id is already stored on user' do
          it 'should return the stored id' do
            expect(notifier.get_team_or_chat_id).to eq(notifier.team_or_chat_id)
          end
        end

        context 'when team_or_chat_id is not stored on user' do
          it 'should make request to get team_or_chat id' do
            notifier.team_or_chat_id = nil

            expect_any_instance_of(Teams::FetchUsersService)
              .to receive(:get_chat_id)
              .and_return({ success: true, chat_id: 'e639ebe8-aa2a-4d4c-9cac-bd910e0b75a8' })

            expect(notifier.get_team_or_chat_id).to eq('e639ebe8-aa2a-4d4c-9cac-bd910e0b75a8')
          end
        end
      end

      context 'when recipient_type is relative' do
        before { notifier.recipient_type = RELATIVE }
        let(:chat_id){ 'abcd' }
        let(:another_user){ create(:user, tenant_id: tenant.id, email: '<EMAIL>') }

        context 'when recipient_id is record_owner' do
          before { notifier.recipient_id = 'record_owner' }

          context 'when user found in database' do
            context 'when agent mapping found in database' do
              context 'when successfully fetched teams_or_chat id' do
                before do
                  agent_mapping.update({ user_id: connected_account.user_id, tenant_id: connected_account.user.tenant_id })
                  expect_any_instance_of(Teams::FetchUsersService)
                    .to receive(:get_chat_id)
                    .and_return({ success: true, chat_id: chat_id })
                end

                it 'should return the team_or_chat_id' do
                  expect(notifier.get_team_or_chat_id(record_owner: connected_account.user.kylas_user_id, tenant_id: connected_account.user.tenant_id))
                  .to eq(chat_id)
                end
              end
            end
          end
        end

        context 'when recipient_id is record_created_by' do
          before { notifier.recipient_id = 'record_created_by' }

          context 'when user found in database' do
            context 'when agent mapping found in database' do
              context 'when successfully fetched teams_or_chat id' do
                before do
                  agent_mapping.update({ user_id: connected_account.user_id, tenant_id: connected_account.user.tenant_id })
                  expect_any_instance_of(Teams::FetchUsersService)
                    .to receive(:get_chat_id)
                    .and_return({ success: true, chat_id: chat_id })
                end

                it 'should return the team_or_chat_id' do
                  expect(notifier.get_team_or_chat_id(record_created_by: connected_account.user.kylas_user_id, tenant_id: connected_account.user.tenant_id))
                  .to eq(chat_id)
                end
              end
            end
          end
        end

        context 'when recipient_id is record_updated_by' do
          before { notifier.recipient_id = 'record_updated_by' }

          context 'when user found in database' do
            context 'when agent mapping found in database' do
              context 'when successfully fetched teams_or_chat id' do
                before do
                  agent_mapping.update({ user_id: connected_account.user_id, tenant_id: connected_account.user.tenant_id })
                  expect_any_instance_of(Teams::FetchUsersService)
                    .to receive(:get_chat_id)
                    .and_return({ success: true, chat_id: chat_id })
                end

                it 'should return the team_or_chat_id' do
                  expect(notifier.get_team_or_chat_id(record_updated_by: connected_account.user.kylas_user_id, tenant_id: connected_account.user.tenant_id))
                  .to eq(chat_id)
                end
              end
            end
          end
        end
      end
    end
  end
end
