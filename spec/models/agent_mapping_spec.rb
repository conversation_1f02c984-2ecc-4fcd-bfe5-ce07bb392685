require 'rails_helper'

RSpec.describe AgentMapping, type: :model do
  describe 'associations' do
    it 'should belongs_to tenant' do
      tenant_association = AgentMapping.reflect_on_association(:tenant)
      expect(tenant_association.macro).to eq(:belongs_to)
    end

    it 'should belongs_to user' do
      user_association = AgentMapping.reflect_on_association(:user)
      expect(user_association.macro).to eq(:belongs_to)
    end

    it 'should belongs_to connected_account' do
      connected_account_association = AgentMapping.reflect_on_association(:connected_account)
      expect(connected_account_association.macro).to eq(:belongs_to)
    end
  end

  describe 'validation' do
    let(:user) { create(:user) }
    let(:agent_mapping){ build(:agent_mapping, user_id: user.id) }

    it 'should validates the presence of name' do
      agent_mapping.name = nil
      agent_mapping.validate
      expect(agent_mapping.errors[:name]).to include("can't be blank")
    end

    it 'should validate the identifier_type' do
      agent_mapping.identifier_type = 'FB'
      agent_mapping.validate
      expect(agent_mapping.errors[:identifier_type]).to include('FB is not a valid type')
    end

    # TODO: Write the rspec for identifier
    it 'should validate the identifier' do; end
  end
end
