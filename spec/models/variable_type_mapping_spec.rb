# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VariableTypeMapping, type: :model do
  describe 'association' do
    it 'should belongs_to notification_template' do
      notification_template_association = VariableTypeMapping.reflect_on_association(:notification_template)
      expect(notification_template_association.macro).to eq(:belongs_to)
    end
  end

  describe 'validation' do
    let(:variable_type_mapping){ build(:variable_type_mapping) }

    it 'should validates the presence of name' do
      variable_type_mapping.name = nil
      expect(variable_type_mapping).to be_invalid
      expect(variable_type_mapping.errors[:name]).to include("can't be blank")
    end

    it 'should validates the presence of variable_type' do
      variable_type_mapping.variable_type = nil
      expect(variable_type_mapping).to be_invalid
      expect(variable_type_mapping.errors[:variable_type]).to include("can't be blank")
    end
  end
end
