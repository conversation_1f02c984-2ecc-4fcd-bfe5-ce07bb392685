require 'rails_helper'

RSpec.describe KylasEngine::Tenant, type: :model do
  describe "association" do
    it 'should has_one connected_account' do
      connected_account_association = KylasEngine::Tenant.reflect_on_association(:connected_account)
      expect(connected_account_association.macro).to eq(:has_one)
      expect(connected_account_association.options).to eq({ :dependent=>:destroy })
    end

    it 'should has_many workflow_action' do
      workflow_action_association = KylasEngine::Tenant.reflect_on_association(:workflow_actions)
      expect(workflow_action_association.macro).to eq(:has_many)
      expect(workflow_action_association.options).to eq({ :dependent=>:destroy })
    end

    it 'should has_many notification_log' do
      notification_log_assocaition = KylasEngine::Tenant.reflect_on_association(:notification_logs)
      expect(notification_log_assocaition.macro).to eq(:has_many)
      expect(notification_log_assocaition.options).to eq({ :dependent=>:destroy })
    end

    it 'should has_many notification_templates' do
      notification_templates_assocaition = KylasEngine::Tenant.reflect_on_association(:notification_templates)
      expect(notification_templates_assocaition.macro).to eq(:has_many)
      expect(notification_templates_assocaition.options).to eq({ :dependent=>:destroy })
    end
  end
end
