require 'rails_helper'

RSpec.describe NotificationLog, type: :model do
  describe "association" do
    it 'should belongs_to tenant' do
      notification_log_assocaition = NotificationLog.reflect_on_association(:tenant)
      expect(notification_log_assocaition.macro).to eq(:belongs_to)
    end
  end

  describe "validation" do
    let(:notification_log) { build(:notification_log) }
    context "when requrired parameters are not provided" do

      it "should validate presence of entity_type" do
        notification_log.entity_type = nil
        notification_log.validate
        expect(notification_log.errors[:entity_type]).to include("can't be blank")
      end

      it "should validate presence of status" do
        notification_log.status = nil
        notification_log.validate
        expect(notification_log.errors[:status]).to include("can't be blank")
      end
    end

    context "when all required parameters provided" do
      it "should increase the count of notification_log by 1 " do
        expect do
          create(:notification_log)
        end.to change(NotificationLog, :count).by(1)
      end
    end
  end

  describe '#entity_url' do
    let(:notification_log){ create(:notification_log) }
    context 'when entity type is DEAL' do
      it 'should return deal entity url' do
        notification_log.entity_type = DEAL
        expect(notification_log.entity_url(45))
          .to eq("https://app-qa.sling-dev.com/sales/deals/details/45")
      end
    end
    context 'when entity type is LEAD' do
      it 'should return deal entity url' do
        notification_log.entity_type = LEAD
        expect(notification_log.entity_url(46))
          .to eq("https://app-qa.sling-dev.com/sales/leads/details/46")
      end
    end
    context 'when entity type is CONTACT' do
      it 'should return deal entity url' do
        notification_log.entity_type = CONTACT
        expect(notification_log.entity_url(47))
          .to eq("https://app-qa.sling-dev.com/sales/contacts/details/47")
      end
    end
    context 'when entity type is CALL_LOG' do
      it 'should return deal entity url' do
        notification_log.entity_type = CALL_LOG
        expect(notification_log.entity_url(48))
          .to eq("https://app-qa.sling-dev.com/sales/calls/list?id=48")
      end
    end
    context 'when entity type is MEETING' do
      it 'should return deal entity url' do
        notification_log.entity_type = MEETING
        expect(notification_log.entity_url(49))
          .to eq("https://app-qa.sling-dev.com/sales/meetings/list?id=49")
      end
    end
  end
end
