require 'rails_helper'

RSpec.describe WorkflowAction, type: :model do
  describe "association" do
    it 'should belongs_to notification_template' do
      notification_template_association = WorkflowAction.reflect_on_association(:notification_template)
      expect(notification_template_association.macro).to eq(:belongs_to)
    end

    it 'should belongs_to tenant' do
      tenant_association = WorkflowAction.reflect_on_association(:tenant)
      expect(tenant_association.macro).to eq(:belongs_to)
    end

    it 'should belongs_to user' do
      user_association = WorkflowAction.reflect_on_association(:user)
      expect(user_association.macro).to eq(:belongs_to)
    end
  end

  describe "validation" do
    let(:workflow_action) { build(:workflow_action) }
    context "when incorrect inputs are provided" do
      it "should validate presence of entity_type" do
        workflow_action.entity_type = nil
        workflow_action.validate
        expect(workflow_action.errors[:entity_type]).to include("can't be blank")
      end
    end
  end
end
