require 'rails_helper'

RSpec.describe KylasEngine::User, type: :model do
  describe "association" do
    it 'should has_one connected_account' do
      connected_account_association = KylasEngine::User.reflect_on_association(:connected_account)
      expect(connected_account_association.macro).to eq(:has_one)
      expect(connected_account_association.options).to eq({ :dependent=>:destroy })
    end

    it 'should has_many workflow_action' do
      workflow_action_association = KylasEngine::User.reflect_on_association(:workflow_actions)
      expect(workflow_action_association.macro).to eq(:has_many)
      expect(workflow_action_association.options).to eq({ :dependent=>:destroy })
    end
  end
end
