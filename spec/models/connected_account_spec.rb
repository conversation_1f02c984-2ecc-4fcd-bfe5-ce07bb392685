require 'rails_helper'

RSpec.describe ConnectedAccount, type: :model do
  describe 'associations' do
    it 'should belongs_to tenant' do
      tenant_association = ConnectedAccount.reflect_on_association(:tenant)
      expect(tenant_association.macro).to eq(:belongs_to)
    end

    it 'should belongs_to user' do
      user_association = ConnectedAccount.reflect_on_association(:user)
      expect(user_association.macro).to eq(:belongs_to)
    end

    it 'should has_may notification_templates' do
      notification_templates_association = ConnectedAccount.reflect_on_association(:notification_templates)
      expect(notification_templates_association.macro).to eq(:has_many)
      expect(notification_templates_association.options).to eq({ :dependent=>:destroy })
    end

    it 'should has_many agent_mappings' do
      agent_mappings_association = ConnectedAccount.reflect_on_association(:agent_mappings)
      expect(agent_mappings_association.macro).to eq(:has_many)
      expect(agent_mappings_association.options).to eq({ :dependent=>:destroy })
    end
  end

  describe 'encrypted_attributes' do
    it 'access_token and refresh_token should be encrypted attributes' do
      encrypted_attributes = ConnectedAccount.encrypted_attributes.to_a
      expect(encrypted_attributes).to match_array(%i[access_token refresh_token])
    end
  end

  describe 'validation' do
    let(:user) { create(:user) }
    let(:connected_account) { build(:connected_account, user_id: user.id, tenant_id: user.tenant_id) }

    context "when required inputs are not provided" do
      it 'should validate presence of access token' do
        connected_account.access_token = nil
        connected_account.validate
        expect(connected_account.errors[:access_token]).to include("can't be blank")
      end

      it 'should validate presence of account type' do
        connected_account.account_type = 'Test'
        expect(connected_account).to be_invalid
        expect(connected_account.errors[:account_type]).to include("Test is not a valid account type")
      end

      it 'should validate presence of refresh_token token' do
        connected_account.refresh_token = nil
        connected_account.validate
        expect(connected_account.errors[:refresh_token]).to include("can't be blank")
      end

      it 'should validate presence of status' do
        connected_account.status= 'Test'
        connected_account.validate
        expect(connected_account.errors[:status]).to include("Test is not a valid status")
      end

      it 'should validate presence of microsoft_user_id' do
        connected_account.microsoft_user_id= nil
        connected_account.validate
        expect(connected_account.errors[:microsoft_user_id]).to include("can't be blank")
      end
    end
  end

  describe "#get_access_token" do
    let(:access_token) { 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyJ9' }
    let(:connected_account) { create(:connected_account, access_token: access_token) }

    context "when access token is not expired" do
      it "should return the saved access token" do
        connected_account
        expect(connected_account.access_token).to eq(access_token)
      end
    end

    context "when access token is expired" do
      context "when successfully get new access token using refresh token" do
        let(:refresh_token) { '0.AVQA8LXsRicSVUehAWTTmgXhxx2Eq9JT-eJIhKsWfkHimPRUAAM' }
        let(:successfully_receive_token) {
          expect_any_instance_of(Teams::FetchNewTokenService).to receive(:get_token)
            .and_return({
              :success => true,
              :value => {
                :access_token => access_token,
                :refresh_token => refresh_token
              }
            }
          )
        }
        before do
          connected_account
          connected_account.expires_at = 1.day.ago
        end

        context "when new access token updated successfully" do
          it "should return the new access token" do
            successfully_receive_token
            expect(connected_account.get_access_token).to eq(access_token)
          end
        end

        context "when failed to update the new access token and refresh token" do
          it "should return the access token as nil" do
            successfully_receive_token
            expect(connected_account).to receive(:update).and_return(false)
            expect(connected_account.get_access_token).to eq(nil)
          end
        end
      end

      context "when error in getting new access token using refresh token" do

        it "should return response as nil" do
          connected_account
          connected_account.expires_at = 1.day.ago
          expect_any_instance_of(Teams::FetchNewTokenService).to receive(:get_token)
            .and_return({ :success => false })
          expect(connected_account.get_access_token).to eq(nil)
        end
      end
    end
  end
end
