require 'rails_helper'

RSpec.describe NotificationTemplate, type: :model do
  describe "association" do
    it 'should belongs_to connected_account' do
      connected_account_association = NotificationTemplate.reflect_on_association(:connected_account)
      expect(connected_account_association.macro).to eq(:belongs_to)
    end

    it 'should belongs_to tenant' do
      tenant_association = NotificationTemplate.reflect_on_association(:tenant)
      expect(tenant_association.macro).to eq(:belongs_to)
    end
  end

  describe "validation" do
    let(:notification_template) { build(:notification_template) }
    context "when required inputs are not provided" do
      it "should validate presence of title" do
        notification_template.title = nil
        notification_template.validate
        expect(notification_template.errors[:title]).to include("can't be blank")
      end

      it "should validate presence of heading" do
        notification_template.heading = nil
        notification_template.validate
        expect(notification_template.errors[:heading]).to include("can't be blank")
      end

      it "should validate presence of content" do
        notification_template.content = nil
        notification_template.validate
        expect(notification_template.errors[:content]).to include("can't be blank")
      end

      it "should validate presence of valid message_type" do
        notification_template.message_type = 'standard'
        expect(notification_template).to be_invalid
        expect(notification_template.errors[:message_type]).to include("standard is not a valid message type")
      end
    end

    context "when correct inputs are provided" do
      it "should increase the count of notification_template by 1 " do
        expect do
          create(:notification_template)
        end.to change(NotificationTemplate, :count).by(1)
      end
    end
  end
end
