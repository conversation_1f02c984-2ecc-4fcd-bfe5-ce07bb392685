# frozen_string_literal: true

FactoryBot.define do
  factory :user, class: 'KylasEngine::User' do
    name { '<PERSON>' }
    email { '<EMAIL>' }
    password { SecureRandom.uuid }
    kylas_access_token { SecureRandom.uuid }
    kylas_refresh_token { SecureRandom.uuid }
    kylas_user_id { rand(10**4) }
    is_tenant { true }
    kylas_access_token_expires_at { 1.hour.from_now }
    active { true }
    tenant
  end

  after(:create) do |data|
    if(data.class.to_s == 'KylasEngine::User')
      data.confirm
    end
  end
end
