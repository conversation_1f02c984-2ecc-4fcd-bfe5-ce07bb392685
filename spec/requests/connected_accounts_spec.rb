require 'rails_helper'
require 'webmock/rspec'

RSpec.describe "ConnectedAccounts", type: :request do
  let(:user) { create(:user) }
  let(:request_url){ get "/connected-account" }
  let(:jwt_response) {[
    {
      "email"=>"<EMAIL>",
      "name"=>"Hardik Jade",
      "oid"=> 'iJKV1QiLCJub-eyJ0eXAiOiJKV1Qi-GciOiJSUzI1NiIsImtpZ'
    }
  ]}

  shared_examples "unauthorized user" do
    context "when user is not logged-in" do
      it "should redirect to login page" do
        request_url
        expect(response.code).to eq("302")
        expect(response.redirect_url).to eq(kylas_engine.new_user_session_url)
      end
    end
  end

  shared_examples "unauthorized request" do
    context "when user has not added an kylas api key" do
      it "should redirect to api_key page" do
        execute_with_resource_sign_in(user) do
          user.tenant.kylas_api_key = nil
          request_url
        end
        expect(response.code).to eq("302")
        expect(response.redirect_url).to eq(kylas_engine.edit_tenant_url(user.tenant))
      end
    end
  end

  describe "#index" do
    let(:connect_request) { get "/connected-account" }

    context "when user is logged-in" do
      context "when user has not connected account" do
        it "should display the connect account button" do
          execute_with_resource_sign_in(user) do
            connect_request
          end
          expect(response.code).to eq("200")
          assert_select 'a', text: 'Connect Account'
        end
      end

      context "when user has connected account" do
        before { create(:connected_account,user_id: user.id, tenant_id: user.tenant_id ) }
        it "should display the table" do
          execute_with_resource_sign_in(user) do
            connect_request
          end
          expect(response.code).to eq('200')
          assert_select 'table'
        end
      end
    end

    it_behaves_like "unauthorized user"

    it_behaves_like "unauthorized request"
  end

  describe "#disconnect" do
    let(:disconnect_request) { post '/connected-account/disconnect' }
    let(:connected_account) { create(:connected_account,user_id: user.id, tenant_id: user.tenant_id) }

    context "when user is signed-in" do
      context "when user has not connected an account" do
        it 'should redirect to connected-account page with alert message' do
          execute_with_resource_sign_in(user) do
            disconnect_request
          end
          expect(response.redirect_url).to eq(connected_accounts_url)
          expect(flash[:danger]).to eq("Account not exist, Invalid request.")
        end
      end

      context "when user has connected an account" do
        let(:notification_template) { create(:notification_template, connected_account_id: connected_account.id) }
        let(:workflow_action) { create(:workflow_action, notification_template_id: notification_template.id, tenant_id: user.tenant_id) }

        context "when notification_templates are present" do
          it 'should delete connected account and associated notification templates' do
            connected_account_id = connected_account.id
            notification_template_id = notification_template.id
            execute_with_resource_sign_in(user) do
              disconnect_request
            end
            expect(ConnectedAccount.exists?(id: connected_account_id)).to eq(true)
            expect(ConnectedAccount.find(connected_account_id).status).to eq(INACTIVE)
          end
        end

        context "when notification_templates & workflow_actions are not present" do
          it "should delete existing connected account and returns success message with redirect to connected account page" do
            connected_account
            execute_with_resource_sign_in(user) do
              expect{
                disconnect_request
              }.to change(ConnectedAccount, :count).by(0)
            end
            expect(response.redirect_url).to eq(connected_accounts_url)
            expect(flash[:success]).to eq('Account disconnected successfully')
          end
        end
      end
    end

    context "when user is not sign-in" do
      before { sign_out_resource(user) }

      it_behaves_like "unauthorized user"

      it_behaves_like "unauthorized request"
    end
  end

  describe "#get_access_token" do
    let(:code){ "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyJ9" }
    let(:get_token_request) { get "/connected-account/connect?code=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyJ9" }

    def token_api_request(status:, body:)
      stub_request(:post, MICROSOFT_TOKEN_URL)
        .with(
          body: {
            "client_id" => Rails.application.credentials.notifier_app.client_id,
            "client_secret" => Rails.application.credentials.notifier_app.client_secret,
            "code" => code,
            "grant_type" => "authorization_code",
            "redirect_uri" => Rails.application.credentials.notifier_app.redirect_url,
            "scope" => MICROSOFT_AUTH_SCOPE
          },
          headers: {
            'Accept'=>'*/*',
            'Accept-Encoding'=>'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
            'Content-Type'=>'application/x-www-form-urlencoded',
            'Host'=>'login.microsoftonline.com',
            'User-Agent'=>'Ruby'
          }
        )
      .to_return(status: status, body: body)
    end


    context "when code is correct" do
      it "should show success flash message" do
        token_api_request(
          status: 200,
          body: file_fixture('microsoft/token_success_response.json')
        )
        execute_with_resource_sign_in(user) do
          expect(JWT).to receive(:decode).and_return(jwt_response)
          get_token_request
        end
        expect(flash[:success]).to eq('Account Connected Successfully')
      end
    end

    context "when code is incorrect" do
      it "should show failure flash message" do
        token_api_request(
          status: 400,
          body: file_fixture('microsoft/token_failure_response.json')
        )
        execute_with_resource_sign_in(user) do
          get_token_request
        end
        expect(flash[:danger]).to eq("Failed to connect account, please try again later.Error Codes: 9002313 AADSTS9002313: Invalid request. Request is malformed or invalid.\r\nTrace ID: dc2bf325-5d30-4faf-aece-cbf82a303d00\r\nCorrelation ID: 62b707c2-8bc5-4d83-b3a7-38cbcaa25458\r\nTimestamp: 2023-05-09 08:15:37Z")
      end
    end

    context "when user did not give consent" do
      let(:connect_request_with_error) {
        get "/connected-account/connect", params: {
            error: "consent_required",
            error_description: "AADSTS65004 User declined to consent to access the app"
          }
      }

      it "should show failure flash message" do
        execute_with_resource_sign_in(user) do
          connect_request_with_error
        end
        expect(flash[:danger]).to eq('Failed to connect account, please try again later.')
      end
    end
  end
end
