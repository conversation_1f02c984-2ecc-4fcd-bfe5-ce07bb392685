require 'rails_helper'
require 'webmock/rspec'

RSpec.describe AgentMappingsController, type: :request do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant_id: tenant.id) }
  let(:connected_account) { create(:connected_account, user_id: user.id, tenant_id: tenant.id) }
  let(:agent_mapping) { create(:agent_mapping, connected_account_id: connected_account.id, user_id: user.id, tenant_id: tenant.id) }

  def fetch_users_request(status:, body:, token:)
    url = "https://graph.microsoft.com/v1.0/users"
    stub_request(:get, url)
    .with(headers: { 'Authorization' => "Bearer #{token}" })
    .to_return(status: status, body: body)
  end

  shared_examples 'unauthenticated user' do
    context 'when user is not logged-in' do
      it 'should redirect to login page' do
        request_url
        expect(response.code).to eq('302')
        expect(response.redirect_url).to eq(kylas_engine.new_user_session_url)
      end
    end
  end

  shared_examples 'unauthorized request' do
    context 'when user has not added an kylas api key' do
      it 'should redirect to api_key page' do
        execute_with_resource_sign_in(user) do
          user.tenant.kylas_api_key = nil
          request_url
        end
        expect(response.code).to eq('302')
        expect(response.redirect_url).to eq(kylas_engine.edit_tenant_url(user.tenant))
      end
    end
  end

  describe '#index' do
    let(:request_url) { get "/connected-account/#{connected_account.id}/agent-mappings" }

    context 'when user is logged in' do
      context 'when user has not connected an account' do
        let(:request_url) { get "/connected-account/1/agent-mappings" }

        it 'should redirect to connected account path with account not connected error message' do
          execute_with_resource_sign_in(user) do
            request_url
            expect(response.code).to eq('302')
            expect(response).to redirect_to 'http://www.example.com/connected-account'
          end
        end
      end

      context 'when user has connected an account' do
        context 'when user did not add any mapping' do
          it 'should show the add agent mapping button' do
            execute_with_resource_sign_in(user) do
              request_url
              anchor_elements = assert_select('a')
              expect(anchor_elements.count).to be(9)
              expect(anchor_elements.map{ |a| a.children.text }).to include('Add Agent')
            end
          end
        end

        context 'when user has already added mapping' do
          it 'should display the mapping in tables' do
            execute_with_resource_sign_in(user) do
              agent_mapping
              request_url
              assert_select 'tr', count: 3
            end
          end
        end
      end
    end

    it_behaves_like 'unauthenticated user'
    it_behaves_like 'unauthorized request'
  end

  describe '#new' do
    let(:request_url) { get "/connected-account/#{connected_account.id}/agent-mappings/new" }

    context 'when user is logged in' do
      context 'when user has not connected an account' do
        let(:request_url) { get "/connected-account/1/agent-mappings/new" }

        it 'should redirect to connected account path with account not connected error message' do
          execute_with_resource_sign_in(user) do
            request_url
            expect(response.code).to eq('302')
            expect(response).to redirect_to 'http://www.example.com/connected-account'
          end
        end
      end

      context 'when user has connected an account' do
        before { fetch_users_request(status: '200', body: file_fixture('teams/get_all_users_success_response.json'), token: connected_account.access_token) }

        it 'should displays form with 2 fields' do
          execute_with_resource_sign_in(user) do
            request_url
            expect(response.code).to eq('200')
            input_elements = assert_select('input')
            expect(input_elements.count).to be(3)
            expect(input_elements.map { |e| e.attributes['value']&.value }).to match_array([nil, TEAMS, 'Save'])

            select_elements = assert_select('select')
            expect(select_elements.count).to be(2)
            expect(select_elements.map { |e| e.attributes['id'].value }).to match_array(%w[agent_mapping_user_id agent_mapping_identifier])
          end
        end
      end
    end

    it_behaves_like 'unauthenticated user'
    it_behaves_like 'unauthorized request'
  end

  describe '#create' do
    let(:request_url) { post "/connected-account/#{connected_account.id}/agent-mappings" }

    context 'when user is logged in' do
      context 'when failed to create agent mapping' do
        context 'when mapping already exists' do
          let(:request_url) { post "/connected-account/#{connected_account.id}/agent-mappings", params: { agent_mapping: { name: 'Test User', tenant_id: tenant.id, identifier: agent_mapping.identifier, user_id: user.id, identifier_type: agent_mapping.identifier_type } } }

          before do
            agent_mapping
            fetch_users_request(status: '200', body: file_fixture('teams/get_all_users_success_response.json'), token: connected_account.access_token)
          end

          it 'should display failed to create agent mapping error message' do
            execute_with_resource_sign_in(user) do
              request_url
              expect(response.code).to eq('200')
              expect(assert_select('.alert-danger').first.children.text.strip).to eq("2 errors prohibited this agent mapping from being saved:\n    \n    \n        Identifier mapping already present\n        User mapping already present")
            end
          end
        end
      end

      context 'when agent mapping created successfully' do
        let(:new_user) { create(:user, email: '<EMAIL>', tenant_id: tenant.id) }
        let(:request_url) { post "/connected-account/#{connected_account.id}/agent-mappings", params: { agent_mapping: { name: 'Test User', tenant_id: tenant.id, identifier: agent_mapping.identifier, user_id: new_user.id, identifier: '402d-e1a5-55fee8c7-8b17740c74aa-bde8', identifier_type: TEAMS } } }

        before { fetch_users_request(status: '200', body: file_fixture('teams/get_all_users_success_response.json'), token: connected_account.access_token) }

        it 'should display agent mapping created successfully success message' do
          execute_with_resource_sign_in(user) do
            request_url
            expect(response.code).to eq('302')
            expect(flash.to_h['success']).to eq('Agent added successfully')
          end
        end
      end
    end

    it_behaves_like 'unauthenticated user'
    it_behaves_like 'unauthorized request'
  end

  describe '#edit' do
    let(:request_url) { get "/connected-account/#{connected_account.id}/agent-mappings/#{agent_mapping.id}/edit" }

    context 'when user is logged in' do
      context 'wher user has not connected an account' do
        let(:request_url) { get "/connected-account/1111/agent-mappings/111/edit" }

        it 'should redirect to connected account path with account not connected error message' do
          execute_with_resource_sign_in(user) do
            request_url
            expect(response.code).to eq('302')
            expect(response).to redirect_to 'http://www.example.com/connected-account'
          end
        end
      end

      context 'wher user has connected an account' do
        before { fetch_users_request(status: '200', body: file_fixture('teams/get_all_users_success_response.json'), token: connected_account.access_token) }

        it 'should displays form with 2 fields with agent mapping pre filled values' do
          execute_with_resource_sign_in(user) do
            request_url
            expect(response.code).to eq('200')
            input_elements = assert_select('input')
            expect(input_elements.count).to be(4)
            expect(input_elements.map { |e| e.attributes['value']&.value }).to match_array(["John Doe", "Save", "TEAMS", "patch"])

            select_elements = assert_select('select')
            expect(select_elements.count).to be(2)
            expect(select_elements.map { |e| e.attributes['id'].value }).to match_array(%w[agent_mapping_user_id agent_mapping_identifier])
          end
        end
      end
    end

    it_behaves_like 'unauthenticated user'
    it_behaves_like 'unauthorized request'
  end

  describe '#update' do
    let(:request_url) { put "/connected-account/#{connected_account.id}/agent-mappings/#{agent_mapping.id}" }

    context 'when user is logged in' do
      let(:request_url) { put "/connected-account/#{connected_account.id}/agent-mappings/#{agent_mapping.id}", params: { agent_mapping: { name: 'Test User', tenant_id: tenant.id, identifier: agent_mapping.identifier, user_id: user.id, identifier_type: agent_mapping.identifier_type } } }

      context 'when failed to update agent mapping' do
        context 'when mapping already exists' do
          before do
            agent_mapping
            fetch_users_request(status: '200', body: file_fixture('teams/get_all_users_success_response.json'), token: connected_account.access_token)
          end

          let(:new_user){ create(:user, email: '<EMAIL>', tenant_id: tenant.id) }
          let(:new_agent_mapping){ create(:agent_mapping, connected_account_id: connected_account.id, user_id: new_user.id, tenant_id: tenant.id) }
          let(:request_url) { put "/connected-account/#{connected_account.id}/agent-mappings/#{new_agent_mapping.id}", params: { agent_mapping: { name: 'Test User', tenant_id: tenant.id, identifier: new_agent_mapping.identifier, user_id: user.id, identifier_type: new_agent_mapping.identifier_type } } }

          it 'should display failed to update agent mapping error message' do
            execute_with_resource_sign_in(user) do
              request_url
              expect(response.code).to eq('200')
              expect(assert_select('.alert-danger').first.children.text.strip).to eq("1 error prohibited this agent mapping from being saved:\n    \n    \n        User mapping already present")
            end
          end
        end
      end

      context 'when agent mapping updated successfully' do
        it 'should display agent mapping successfully updated success message' do
          execute_with_resource_sign_in(user) do
            request_url
            expect(response.code).to eq('302')
            expect(flash.to_h['success']).to eq('Agent update successfully')
          end
        end
      end
    end

    it_behaves_like 'unauthenticated user'
    it_behaves_like 'unauthorized request'
  end

  describe '#destroy' do
    let(:request_url) { delete "/connected-account/#{connected_account.id}/agent-mappings/#{agent_mapping.id}" }

    context 'when user is logged in' do
      context 'when failed to destroy agent mapping' do
        let(:request_url) { delete "/connected-account/#{connected_account.id}/agent-mappings/123" }
        context 'when invalid agent mapping id is passed' do
          it 'should redirect to agent mapping path with failed to delete agent mapping message' do
            execute_with_resource_sign_in(user) do
              request_url
              expect(response.code).to eq('302')
              expect(response).to redirect_to("http://www.example.com/connected-account/#{connected_account.id}/agent-mappings")
            end
          end
        end
      end

      context 'when agent mapping successfully deleted' do
        it 'should display agent mapping destroyed successfully message' do
          execute_with_resource_sign_in(user) do
            request_url
            expect(response.code).to eq('302')
            expect(flash.to_h['success']).to eq('Agent destroy successfully')
          end
        end
      end
    end

    it_behaves_like 'unauthenticated user'
    it_behaves_like 'unauthorized request'
  end

  describe "#autocomplete" do
    let(:request_url) { get "/connected-account/#{connected_account.id}/agent-mappings/autocomplete", params: { query: 'mohan' }, xhr: true }

    context 'when user is logged in' do
      context 'when user has not connected an account' do
        let(:request_url) { get "/connected-account/1111/agent-mappings/autocomplete" }

        it 'should redirect to connected account path with account not connected error message' do
          execute_with_resource_sign_in(user) do
            request_url
            expect(response.code).to eq('302')
            expect(response).to redirect_to 'http://www.example.com/connected-account'
          end
        end
      end

      context 'when user has connected an account' do
        context 'when successfully fetched the users' do
          before { expect_any_instance_of(Teams::FetchUsersService).to receive(:fetch_users_by_name).and_return({ :success => true, :users => [[[],[]], "Users"]}) }
          it 'should return success as true' do
            execute_with_resource_sign_in(user) do
              request_url
              expect(response.code).to eq('204')
            end
          end
        end
      end
    end

    it_behaves_like 'unauthorized request'
  end
end
