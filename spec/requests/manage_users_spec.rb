require 'rails_helper'

RSpec.describe "ManageUsersController", type: :request do
  let(:user) { create(:user) }
  let(:request_url){ get "/manage-users" }

  shared_examples "unauthorized user" do
    context "when user is not logged-in" do
      it "should redirect to login page" do
        request_url
        expect(response.code).to eq("302")
        expect(response.redirect_url).to eq(kylas_engine.new_user_session_url)
      end
    end
  end

  shared_examples "unauthorized request" do
    context "when user has not added an kylas api key" do
      it "should redirect to api_key page" do
        execute_with_resource_sign_in(user) do
          user.tenant.kylas_api_key = nil
          request_url
        end
        expect(response.code).to eq("302")
        expect(response.redirect_url).to eq(kylas_engine.edit_tenant_url(user.tenant))
      end
    end
  end

  describe "#index" do
    context "when user is logged-in" do
      context "when only tenant user is present" do
        it "displays single user in table" do
          execute_with_resource_sign_in(user) do
            request_url
            assert_select 'tr', count: 3
          end
        end
      end
    end

    it_behaves_like "unauthorized user"
    it_behaves_like "unauthorized request"
  end

  describe "#edit" do
    context 'when user is logged in' do
      context 'when edit user is requested' do
        context "when valid user id is passed" do
          before do
            expect_any_instance_of(Kylas::FetchUserDetailService).to receive(:get_another_users_details)
              .and_return({ :success => true, :details => { firstName: 'John', lastName: 'Doe' } }
            )
          end

          let(:request_url) { get "/manage-users/#{user.id}/edit" }
          it 'should displays form with 3 fields pefilled with user values' do
            execute_with_resource_sign_in(user) do
              request_url
              expect(response.code).to eq('200')
              input_elements = assert_select('input')
              expect(input_elements.count).to be(4)
              expect(input_elements.map { |e| e.attributes['value'].value }).to match_array(['patch', user.email, user.name, 'Save'])

              select_elements = assert_select('select')
              expect(select_elements.count).to be(1)
              expect(select_elements.map { |e| e.attributes['id'].value }).to match_array(%w[user_kylas_user_id])
              expect(select_elements.first.children.count).to be(3)
            end
          end
        end

        context "when invalid user id is passed" do
          let(:request_url) { get "/manage-users/9999/edit" }
          it 'should redirect you to user listing page with error message' do
            execute_with_resource_sign_in(user) do
              request_url
              expect(response.code).to eq('302')
              expect(flash.to_hash['danger']).to eq(I18n.t('manage_users.invalid_user_provided'))
            end
          end
        end
      end
    end

    it_behaves_like 'unauthorized user'
    it_behaves_like 'unauthorized request'
  end

  describe "#update" do
    context "when user is logged-in" do
      context 'when update user is requested' do
        context "when invalid params are passed" do
          context "when invalid user id is passed" do
            let(:request_url) { put "/manage-users/9999", params: { user: { name: 'User Name', email: '<EMAIL>', kylas_user_id: 2 } }, xhr: true }
            it 'should redirect you to user listing page with error message' do
              execute_with_resource_sign_in(user) do
                request_url
                expect(response.code).to eq('302')
                expect(flash.to_hash['danger']).to eq(I18n.t('manage_users.invalid_user_provided'))
              end
            end
          end
        end

        context "when valid params are passed" do
          let(:request_url) { put "/manage-users/#{user.id}", params: { user: { name: 'User Name', email: '<EMAIL>', kylas_user_id: 2 } }, xhr: true }
          it 'updates user and redirects to users listing' do
            execute_with_resource_sign_in(user) do
              request_url
              expect(response.code).to eq('302')
              expect(flash.to_hash['success']).to eq(I18n.t('manage_users.success', action: "updated"))
            end
          end
        end
      end
    end

    it_behaves_like 'unauthorized user'
    it_behaves_like 'unauthorized request'
  end

  describe "#sync" do
    let(:request_url) { post "/manage-users/sync" }

    context "when unable to fetch and create users" do
      context "when user fetching is already in progress" do
        it "should redirect to user listing page with error message" do
          user.tenant.update_column(:fetch_and_create_users_job, '123456789')
          execute_with_resource_sign_in(user) do
            request_url
            expect(response.code).to eq('302')
            expect(flash.to_hash['danger']).to eq(I18n.t('manage_users.user_fetch_in_progress'))
          end
        end
      end
    end

    context "when successfully fetched and created users" do
      it "should redirect to user listing page with success message" do
        execute_with_resource_sign_in(user) do
          request_url
          expect(response.code).to eq('302')
          expect(flash.to_hash['success']).to eq(I18n.t('manage_users.fetching_scheduled'))
        end
      end
    end

    it_behaves_like 'unauthorized user'
    it_behaves_like 'unauthorized request'
  end
end
