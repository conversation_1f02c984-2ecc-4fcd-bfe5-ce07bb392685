require 'rails_helper'
require 'webmock/rspec'


RSpec.describe "NotificationLogs", type: :request do
  let(:user) { create(:user) }
  let(:valid_request) { get '/notification-logs' }
  let(:notification_logs) { create(:notification_log, tenant_id: user.tenant.id ) }

  shared_examples "unauthorized user" do
    context "when user is not logged-in" do
      it "should redirect to login page" do
        valid_request
        expect(response.code).to eq("302")
        expect(response.redirect_url).to eq(kylas_engine.new_user_session_url)
      end
    end
  end

  shared_examples "unauthorized request" do
    context "when user has not added an kylas api key" do
      it "should redirect to api_key page" do
        execute_with_resource_sign_in(user) do
          user.tenant.kylas_api_key = nil
          valid_request
        end
        expect(response.code).to eq("302")
        expect(response.redirect_url).to eq(kylas_engine.edit_tenant_url(user.tenant))
      end
    end
  end

  describe "#index" do
    it_behaves_like "unauthorized user"

    it_behaves_like "unauthorized request"

    context "when user is logged-in" do
      context "when notification logs is blank" do
        it "should display 'Notification Logs not found!'" do
          execute_with_resource_sign_in(user) do
            valid_request
            assert_select 'h4', text: 'Notification Logs not found!'
          end
        end
      end

      context "when notification logs are found" do
        it "should show the table of notification logs" do
          execute_with_resource_sign_in(user) do
            notification_logs
            valid_request
            assert_select 'tr', count: 2
          end
        end
      end
    end
  end
end
