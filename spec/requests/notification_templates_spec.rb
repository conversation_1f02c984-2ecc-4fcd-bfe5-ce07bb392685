require 'rails_helper'
require 'webmock/rspec'

RSpec.describe 'NotificationTemplates', type: :request do
  let(:user) { create(:user) }
  let(:connected_account){ create(:connected_account, tenant_id: user.tenant.id, user_id: user.id) }
  let(:notification_template){ create(:notification_template, connected_account_id: connected_account.id, tenant_id: user.tenant.id) }
  let(:valid_index_request){ get '/notification-templates' }
  let(:valid_new_request){ get '/notification-templates/new' }
  let(:request_params) do
    {
      notification_template: {
        title: 'New deal template',
        entity_type: 'DEAL',
        heading: 'Name of Deal',
        notifiers: ['channel|*|General|*|19:qfw0waTHtls_StwwTshqcz2DzX2s1@thread.tacv2|*|19e98001-da32-463b-4da8-StwwTshqcz2DzX2s'],
        content: 'Congratulation we got an deal',
        view_entity_cta: true,
        entity_fields: [
          { :displayName => "Name", :name => "name", :isStandard => true },
          { :displayName => "Forecasting Type", :name => "forecastingType", :isStandard => true  },
          { :displayName => "Owner", :name => "ownedBy", :isStandard => true }
        ].to_json
      }
    }
  end

  let(:fetch_content_params) do
    {
      entity_fields: [
        { :displayName => "Name", :name => "name", :isStandard => true },
        { :displayName => "Forecasting Type", :name => "forecastingType", :isStandard => true  },
        { :displayName => "Owner", :name => "ownedBy", :isStandard => true }
      ].to_json,
      template_id: 55
    }
  end

  def edit_request(template_id = nil)
    get "/notification-templates/#{template_id}/edit"
  end

  def clone_request(template_id = nil)
    get "/notification-templates/#{template_id}/clone"
  end

  def fetch_mention_users_request
    get "/notification-templates/fetch-mention-users", params: { query: 'user' }, headers: { 'HTTP_ACCEPT' => 'application/json' }
  end

  def create_request(params:)
    post '/notification-templates', params: params
  end

  def update_request(id:, params:)
    patch "/notification-templates/#{id}", params: params
  end

  def fetch_variables_request(entity_type = DEAL)
    get "/notification-templates/#{entity_type}/fetch-variables", headers: {
      'HTTP_ACCEPT' => "application/json"
    }
  end

  def fetch_content_request(params:)
    post '/notification-templates/fetch-content', params: params, headers: {
      'HTTP_ACCEPT' => "application/json"
    }
  end

  shared_examples "unauthorized user" do
    context "when user is not logged-in" do
      it "should redirect to login page" do
        valid_index_request
        expect(response.code).to eq("302")
        expect(response.redirect_url).to eq(kylas_engine.new_user_session_url)
      end
    end
  end

  shared_examples "unauthorized request" do
    context "when user has not added an kylas api key" do
      it "should redirect to api_key page" do
        execute_with_resource_sign_in(user) do
          user.tenant.kylas_api_key = nil
          valid_index_request
        end
        expect(response.code).to eq("302")
        expect(response.redirect_url).to eq(kylas_engine.edit_tenant_url(user.tenant))
      end
    end
  end

  describe '#index' do
    it_behaves_like "unauthorized user"

    it_behaves_like "unauthorized request"

    context "when user is logged-in" do
      context "when user has not connected an account" do
        it "should show the add notification button" do
          execute_with_resource_sign_in(user) do
            valid_index_request
            expect(response.code).to eq('200')
            assert_select 'a', text: /Add Notification Template/
          end
        end
      end

      context "when user connected account is inactive" do
        context "when user has already created notification templates" do
          it "should show the notification template table" do
            execute_with_resource_sign_in(user) do
              notification_template
              valid_index_request
              assert_select 'tr', count: 2
            end
          end
        end

        context "when user has not created any notification template" do
          it "should show the add notification button" do
            execute_with_resource_sign_in(user) do
              valid_index_request
              expect(response.code).to eq('200')
              assert_select 'a', text: /Add Notification Template/
            end
          end
        end
      end

      context "when notification template is blank" do
        it "should display 'Notification Template not found'" do
          execute_with_resource_sign_in(user) do
            user.connected_account = connected_account
            valid_index_request
            assert_select 'h4', text: 'Notification Templates not found!'
          end
        end
      end

      context "when notification templates are found" do
        it "should show the table of notification templates" do
          execute_with_resource_sign_in(user) do
            notification_template
            valid_index_request
            assert_select 'tr', count: 2
          end
        end
      end
    end
  end

  describe "#new" do
    it_behaves_like "unauthorized user"
    it_behaves_like "unauthorized request"

    context "when user is logged-in" do
      context "when user has not connected an account" do
        it "should redirect to connected-account page with error flash message." do
          execute_with_resource_sign_in(user) do
            valid_new_request
            expect(response.code).to eq('302')
            expect(flash[:danger]).to eq('Account not connected or account is inactive, Please connect your account first')
          end
        end
      end

      context "when user connected account is inactive" do
        it "should redirect to connected account page with danger flash message" do
          execute_with_resource_sign_in(user) do
            connected_account.status = INACTIVE
            user.connected_account = connected_account
            valid_new_request
            expect(response.code).to eq('302')
            expect(flash[:danger]).to eq('Account not connected or account is inactive, Please connect your account first')
          end
        end
      end

      context "when successfully fetched channel and account is connected" do
        it "should render the notification template form" do
          execute_with_resource_sign_in(user) do
            connected_account
            valid_new_request
            assert_select 'form'
          end
        end
      end
    end
  end

  describe "#create" do
    it_behaves_like "unauthorized user"
    it_behaves_like "unauthorized request"

    context "when user is logged-in" do
      context "when user has not connected an account" do
        it "should redirect to connected-account page with error flash message." do
          execute_with_resource_sign_in(user) do
            create_request(params: request_params)
            expect(response.code).to eq('302')
            expect(flash[:danger]).to eq('Account not connected or account is inactive, Please connect your account first')
          end
        end
      end

      context "when user connected account is inactive" do
        it "should redirect to connected account page with danger flash message" do
          execute_with_resource_sign_in(user) do
            connected_account.status = INACTIVE
            user.connected_account = connected_account
            create_request(params: request_params)
            expect(response.code).to eq('302')
            expect(flash[:danger]).to eq('Account not connected or account is inactive, Please connect your account first')
          end
        end
      end

      context "when entity fields are not passed" do
        it "should redirect to notification template page with error flash message" do
          execute_with_resource_sign_in(user) do
            user.connected_account = connected_account
            request_params[:notification_template][:entity_fields] = nil
            create_request(params: request_params)
            expect(response.code).to eq('302')
            expect(flash[:danger]).to eq('Unable to fetch entity fields, Please try again later!')
          end
        end
      end

      context "when all parameters are validated" do
        it "should create the notification template with all passed parameters" do
          execute_with_resource_sign_in(user) do
            notification_template
            request_params[:notification_template][:template_id] = notification_template.id
            expect{ create_request(params: request_params) }.to change(NotificationTemplate, :count).by(1)
          end
        end
      end
    end
  end

  describe "#edit" do
    it_behaves_like "unauthorized user"

    it_behaves_like "unauthorized request"

    context "when user is logged-in" do
      context "when failed to fetch notifiaction template" do
        context "when account is not connected" do
          it "should redirect to connected-account page with error flash message." do
            execute_with_resource_sign_in(user) do
              edit_request(10)
              expect(response.code).to eq('302')
              expect(flash[:danger]).to eq('Account not connected or account is inactive, Please connect your account first')
            end
          end
        end

        context "when user connected account is inactive" do
          it "should redirect to connected account page with danger flash message" do
            execute_with_resource_sign_in(user) do
              connected_account.status = INACTIVE
              user.connected_account = connected_account
              edit_request(50)
              expect(response.code).to eq('302')
              expect(flash[:danger]).to eq('Account not connected or account is inactive, Please connect your account first')
            end
          end
        end

        context "when template is missing for given template id" do
          it "should redirect to notification template page with 'Template not found' error flash message" do
            execute_with_resource_sign_in(user) do
              notification_template
              edit_request(10)
              expect(response.code).to eq('302')
              expect(flash[:danger]).to eq('Notification Templates not found!')
            end
          end
        end

        context "when failed to fetch entity field for given entity type" do
          it "should redirect to notification template page with error flash message" do
            expect_any_instance_of(Kylas::FetchEntityFieldService).to receive(:fetch_field).and_return({
              :success => false,
              :error => 'Error in fetching fields for provided entity type'
            })

            execute_with_resource_sign_in(user) do
              notification_template
              edit_request(notification_template.id)
              expect(response.code).to eq('302')
              expect(flash[:danger]).to eq('Error in fetching fields for provided entity type')
            end
          end
        end
      end

      context "when successfully fetched fields and template is found" do
        it "should show the notification template with all saved parameters" do
          execute_with_resource_sign_in(user) do
            notification_template
            expect_any_instance_of(Kylas::FetchEntityFieldService).to receive(:fetch_field).and_return({
              :success => true,
              :data => [
                {
                  :name => "name",
                  :displayName => "Name",
                  :standard => true
                }
              ]
            })

            edit_request(notification_template.id)
            assert_select 'form'
          end
        end
      end
    end
  end

  describe "#update" do
    it_behaves_like "unauthorized user"
    it_behaves_like "unauthorized request"

    context "when user is logged-in" do
      context "when user has not connected an account" do
        it "should redirect to connected-account page with error flash message." do
          execute_with_resource_sign_in(user) do
            user.connected_account = nil
            update_request(id: 10, params: request_params)
            expect(response.code).to eq('302')
            expect(flash[:danger]).to eq('Account not connected or account is inactive, Please connect your account first')
          end
        end
      end

      context "when user connected account is inactive" do
        it "should redirect to connected account page with danger flash message" do
          execute_with_resource_sign_in(user) do
            connected_account.status = INACTIVE
            user.connected_account = connected_account
            update_request(id: 50, params: request_params)
            expect(response.code).to eq('302')
            expect(flash[:danger]).to eq('Account not connected or account is inactive, Please connect your account first')
          end
        end
      end

      context "when entity fields are not passed" do
        it "should redirect to notification template page with error flash message" do
          execute_with_resource_sign_in(user) do
            notification_template
            request_params[:notification_template][:entity_fields] = nil
            update_request(id: notification_template.id, params: request_params)
            expect(response.code).to eq('302')
            expect(flash[:danger]).to eq('Unable to fetch entity fields, Please try again later!')
          end
        end
      end

      context "when all parameters are validated" do
        it "should update the notification template with all passed parameters" do
          execute_with_resource_sign_in(user) do
            notification_template
            request_params[:notification_template][:template_id] = notification_template.id
            expect{ update_request(id: notification_template.id, params: request_params) }.to change(NotificationTemplate, :count).by(0)
          end
        end
      end
    end
  end

  describe "#fetch_variables" do
    it_behaves_like "unauthorized user"

    it_behaves_like "unauthorized request"

    context "when user is logged-in" do
      context "should return success as false" do
        it "when entity type is invalid" do
          execute_with_resource_sign_in(user) do
            fetch_variables_request('ABC')
            expect(response.code).to eq('204')
          end
        end
      end

      context "when entity type is passed" do
        it "should fetch entity fields of given entity type" do
          execute_with_resource_sign_in(user) do
            expect_any_instance_of(Kylas::FetchEntityFieldService)
            .to receive(:fetch_field)
            .and_return({
              :success => true,
              :data => [
                {
                  :id => 118549,
                  :type => "TEXT_FIELD",
                  :name => "name",
                  :displayName => "Name",
                  :sortable => true,
                  :filterable => true,
                  :required => true,
                  :active => true,
                  :standard => true,
                  :min => 3,
                  :max => 255,
                  :internal => false,
                  :systemRequired => false
                }
              ]
            })

            fetch_variables_request
            expect(response.body).to eq("{\"success\":true,\"entity_fields\":\"[{\\\"displayName\\\":null,\\\"name\\\":null,\\\"isStandard\\\":null,\\\"type\\\":null}]\"}")
          end
        end
      end
    end
  end

  describe "#fetch_content" do
    it_behaves_like "unauthorized user"
    it_behaves_like "unauthorized request"

    context "when user is logged-in" do
      context "when failed to fetch content" do
        context "when entity fields are not passed" do
          it "should not return content" do
            fetch_content_params[:entity_fields] = nil
            execute_with_resource_sign_in(user) do
              fetch_content_request(params: fetch_content_params)
              expect(response.code).to eq('204')
              expect(response.body).to eq('')
            end
          end
        end

        context "when notification template not found" do
          it "should not return content" do
            fetch_content_params[:template_id] = nil
            execute_with_resource_sign_in(user) do
              fetch_content_request(params: fetch_content_params)
              expect(response.code).to eq('204')
              expect(response.body).to eq('')
            end
          end
        end
      end

      context "when successfully fetch notification template content" do
        it "should convert internal name to display name of content and return" do
          notification_template
          fetch_content_params[:template_id] = notification_template.id
          execute_with_resource_sign_in(user) do
            fetch_content_request(params: fetch_content_params)
            expect(response.code).to eq('200')
            expect(response.body).to eq("{\"success\":true,\"content\":\"please have a look for lead\"}")
          end
        end
      end
    end
  end

  describe "#clone" do
    context "when user is logged-in" do
      context "when failed to fetch notifiaction template" do
        context "when account is not connected" do
          it "should redirect to connected-account page with error flash message." do
            execute_with_resource_sign_in(user) do
              clone_request(10)
              expect(response.code).to eq('302')
              expect(flash[:danger]).to eq('Account not connected or account is inactive, Please connect your account first')
            end
          end
        end

        context "when user connected account is inactive" do
          it "should redirect to connected account page with danger flash message" do
            execute_with_resource_sign_in(user) do
              connected_account.status = INACTIVE
              user.connected_account = connected_account
              clone_request(50)
              expect(response.code).to eq('302')
              expect(flash[:danger]).to eq('Account not connected or account is inactive, Please connect your account first')
            end
          end
        end

        context "when template is missing for given template id" do
          it "should redirect to notification template page with 'Template not found' error flash message" do
            execute_with_resource_sign_in(user) do
              notification_template
              clone_request(10)
              expect(response.code).to eq('302')
              expect(flash[:danger]).to eq('Notification Templates not found!')
            end
          end
        end

        context "when failed to fetch entity field for given entity type" do
          it "should redirect to notification template page with error flash message" do
            expect_any_instance_of(Kylas::FetchEntityFieldService).to receive(:fetch_field).and_return({
              :success => false,
              :error => 'Error in fetching fields for provided entity type'
            })

            execute_with_resource_sign_in(user) do
              notification_template
              clone_request(notification_template.id)
              expect(response.code).to eq('302')
              expect(flash[:danger]).to eq('Error in fetching fields for provided entity type')
            end
          end
        end
      end

      context "when successfully fetched fields and template is found" do
        it "should show the new notification template with all saved parameters with clone in the title" do
          execute_with_resource_sign_in(user) do
            expect_any_instance_of(Kylas::FetchEntityFieldService).to receive(:fetch_field).and_return({
              :success => true,
              :data => [
                {
                  :name => "name",
                  :displayName => "Name",
                  :standard => true
                }
              ]
            })

            notification_template
            clone_request(notification_template.id)
            expect(assert_select('#notification_template_title').first.attributes['value'].text).to eq("#{notification_template.title}(Copy)")
          end
        end
      end
    end

    it_behaves_like "unauthorized user"
    it_behaves_like "unauthorized request"
  end

  describe "#fetch_mention_users" do
    context "when user is logged in" do
      context "when failed to fetch the mention users" do
        context "when account is not connected" do
          it "should redirect to connected-account page with error flash message." do
            execute_with_resource_sign_in(user) do
              fetch_mention_users_request
              expect(response.code).to eq('302')
              expect(flash[:danger]).to eq('Account not connected or account is inactive, Please connect your account first')
            end
          end
        end

        context "when received success as false from TemplatePayload#mention_users service" do
          before do
            connected_account
            expect_any_instance_of(Teams::TemplatePayload).to receive(:mention_users).and_return({ success: false, message: 'Failed to fetch the users' })
          end

          it "should receive success as false and return with error message" do
            execute_with_resource_sign_in(user) do
              fetch_mention_users_request
              expect(response.body).to eq("{\"success\":false,\"message\":\"Failed to fetch the users\"}")
            end
          end
        end
      end

      context "when mention users fetched successfully" do
        before do
          connected_account
          expect_any_instance_of(Teams::TemplatePayload).to receive(:mention_users).and_return({
            success: true,
            users: [ { id: "{{@John Doe<!-- {{0abb2861-d43c90e937d5}} -->}}", text: "John Doe (<EMAIL>)"}  ]
          })
        end

        it "should receive the success as true with required users" do
          execute_with_resource_sign_in(user) do
            fetch_mention_users_request
            expect(response.body).to eq("{\"success\":true,\"users\":[{\"id\":\"{{@John Doe\\u003c!-- {{0abb2861-d43c90e937d5}} --\\u003e}}\",\"text\":\"John Doe (<EMAIL>)\"}]}")
          end
        end
      end
    end

    it_behaves_like "unauthorized user"
    it_behaves_like "unauthorized request"
  end
end
