require 'rails_helper'
require 'webmock/rspec'

RSpec.describe :WorkflowActions, type: :request do
  let(:user) { create(:user) }
  let(:connected_account) { create(:connected_account, user_id: user.id, tenant_id: user.tenant_id ) }
  let(:notification_template) { create(:notification_template, connected_account_id: connected_account.id, tenant_id: user.tenant.id) }
  let(:workflow_action) { create(:workflow_action, notification_template_id: notification_template.id, tenant_id: user.tenant_id, user_id: user.id) }

  let(:invalid_fetch_request) { get '/workflow-actions/fetch-or-build' }

  def valid_fetch_request(resource_id = nil)
    get '/workflow-actions/fetch-or-build', params: {
      tenantId: user.tenant.kylas_tenant_id,
      userId: user.kylas_user_id,
      entityType: "DEAL",
      identifier: "5.***************",
      resourceId: resource_id
    }
  end

  def field_api_request(url:, status:, body:)
    stub_request(:get, url)
      .with(headers: { 'api-key' => user.tenant.kylas_api_key })
      .to_return(status: status, body: body)
  end

  def fetch_joined_teams_request(url:, status:, body:)
    stub_request(:get, url)
    .with(headers: { 'Authorization' => "Bearer 123456" })
    .to_return(status: status, body: body)
  end

  shared_examples "unauthorized user" do
    context "when user is not logged-in" do
      it "should redirect to login page" do
        valid_fetch_request
        expect(response.code).to eq("302")
        expect(response.redirect_url).to eq(kylas_engine.new_user_session_url)
      end
    end
  end

  shared_examples "unauthorized request" do
    context "when user has not added an kylas api key" do
      it "should redirect to api_key page" do
        execute_with_resource_sign_in(user) do
          user.tenant.kylas_api_key = nil
          valid_fetch_request
        end
        expect(response.code).to eq("302")
        expect(response.redirect_url).to eq(kylas_engine.edit_tenant_url(user.tenant))
      end
    end
  end

  describe "#fetch_or_build" do
    it_behaves_like "unauthorized user"

    it_behaves_like "unauthorized request"

    context "when user is logged-in" do
      context "when required parameters are not passed" do
        it "should show errors flash message to provide required parameters" do
          execute_with_resource_sign_in(user) do
            user.connected_account = connected_account
            invalid_fetch_request
          end
          assert_select 'li', count: 5
          expect(assert_select('li').children.map(&:text)).to eq(["User ID is missing", "Tenant ID is missing", "Entity Type is missing", "Identifier is missing", "Please connect with the App Developer or Kylas Notifier Support, If you faced any issues."] )
        end
      end

      context "when all parameters are validated" do
        context "when user has not connected an account" do
          it "should show errors flash message to connect account" do
            execute_with_resource_sign_in(user) do
              valid_fetch_request
            end
            assert_select 'li', count: 2
            expect(assert_select('li').children.map(&:text)).to eq(
              [
                "All connected accounts either in-active or no account found. Please add/active at least one account.",
                "Please connect with the App Developer or Kylas Notifier Support, If you faced any issues."
              ]
            )
          end
        end

        context "when user has connected an account" do
          it "should create new workflow action when resource id is blank" do
            field_api_request(
              url: "#{API_URL}/#{API_VERSION}/deals/fields",
              status: 200,
              body: file_fixture('kylas/lead_field_success_response.json')
            );
            execute_with_resource_sign_in(user) do
              notification_template
              valid_fetch_request
            end
            assert_select 'form'
          end

          context "when resource id is present" do
            it "should show an error message when resource id is incorrect" do
              execute_with_resource_sign_in(user) do
                connected_account
                field_api_request(
                  url: "#{API_URL}/#{API_VERSION}/deals/fields",
                  status: 200,
                  body: file_fixture('kylas/lead_field_success_response.json')
                );
                valid_fetch_request('999')
              end
              assert_select 'li', count: 2
              elements = expect(assert_select('li').children.map(&:text)).target
              expect(elements).to eq(["Workflow action is not found, please create another marketplace action", "Please connect with the App Developer or Kylas Notifier Support, If you faced any issues."])
            end

            it "should show the existing notification template fields when resource id is correct" do
              execute_with_resource_sign_in(user) do
                connected_account
                workflow_action
                field_api_request(
                  url: "#{API_URL}/#{API_VERSION}/deals/fields",
                  status: 200,
                  body: file_fixture('kylas/lead_field_success_response.json')
                );
                valid_fetch_request(workflow_action.id)
              end
              assert_select 'form', count: 1
            end
          end
        end
      end
    end
  end

  describe "#save" do
    it_behaves_like "unauthorized user"

    it_behaves_like "unauthorized request"

    context "when user is logged-in" do
      def valid_save_request(template_id = nil, resource_id = nil)
        post '/workflow-actions/save', params: {
          workflow_action: {
            tenant_id: '123',
            userId: '133',
            identifier: '3.***********',
            resourceId: resource_id,
            heading: 'Template for new Deal',
            content: 'Congratulation for new Deal',
            entity_fields: [
                {
                  "displayName" => "Forecasting Type",
                  "name" => "forecastingType"
                },
                {
                  "displayName" => "Owner",
                  "name" => "ownedBy"
                }
              ].to_json,
            entity_type: "DEAL",
            templateId: template_id,
            notification_template_id: template_id
          }
        }
      end

      context "when all parameters are validated" do
        context "when user has not connected an account" do
          it "should show the danger message to connect the account" do
            execute_with_resource_sign_in(user) do
              valid_save_request
            end
            elements = assert_select 'div.alert'
            expect(elements.text.squish).to eq("All connected accounts either in-active or no account found. Please add/active at least one account. Something went wrong. Please try again later or contact Kylas support")
            expect(response.code).to eq("200")
          end
        end

        context "when user has connected an account" do
          it "should return template id missing danger flash message when template id is blank" do
            execute_with_resource_sign_in(user) do
              connected_account
              valid_save_request
            end
            elements = assert_select 'div.alert'
            expect(elements.text.squish).to eq("Template Id is missing Something went wrong. Please try again later or contact Kylas support")
            expect(response.code).to eq("200")
          end

          it "should create new workflow when resource id is blank" do
            execute_with_resource_sign_in(user) do
              notification_template
              field_api_request(
                url: "#{API_URL}/#{API_VERSION}/deals/fields",
                status: 200,
                body: file_fixture('kylas/lead_field_success_response.json')
              )
              valid_save_request(notification_template.id)
            end
            element = assert_select 'h5'
            expect(element.text).to eq("Please Wait...")
            expect(response.code).to eq("200")
          end

          context "when resource id present" do
            it "should return error message if resource id is incorrect" do
              execute_with_resource_sign_in(user) do
                connected_account
                valid_save_request(notification_template.id, '999')
              end
              assert_select 'li', count: 2
              elements = expect(assert_select('li').children.map(&:text)).target
              expect(elements.map(&:squish)).to eq(["Workflow not found", "Something went wrong. Please try again later or contact Kylas support"])
            end

            it "should update the existing workflow with new parameters" do
              field_api_request(
                url: "#{API_URL}/#{API_VERSION}/deals/fields",
                status: 200,
                body: file_fixture('kylas/lead_field_success_response.json')
              )
              execute_with_resource_sign_in(user) do
                connected_account
                workflow_action
                valid_save_request(notification_template.id, workflow_action.id)
                expect{ valid_save_request(notification_template.id, workflow_action.id) }
                  .to change(WorkflowAction, :count).by(0)
              end
              element = assert_select 'h5'
              expect(element.text).to eq("Please Wait...")
              expect(response.code).to eq("200")
            end
          end
        end
      end

      context "when required parameters are not passed" do
        let(:invalid_save_request) { post '/workflow-actions/save' }

        it "should raise an 'ParameterMissing Exception'" do
          execute_with_resource_sign_in(user) do
            connected_account
            invalid_save_request
            elements = assert_select 'div.alert'
            expect(elements.text.squish).to eq('User ID is missing Tenant ID is missing Entity Type is missing Identifier is missing Something went wrong. Please try again later or contact Kylas support')
          end
        end
      end
    end
  end
end
