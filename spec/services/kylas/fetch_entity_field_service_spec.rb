require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Kylas::FetchEntityFieldService do
  let(:user) { create(:user) }

  def field_api_request(url:, status:, body:)
    stub_request(:get, url)
      .with(headers: {
        'api-key' => user.tenant.kylas_api_key
      })
      .to_return(status: status, body: body)
  end

  describe "#fetch_field" do
    context "when entity type is deal" do
      context "when get failed response from field API" do
        context "when api-key is invalid" do
          it "should return success as false" do
            field_api_request(
              url: "#{API_URL}/#{API_VERSION}/deals/fields",
              status: 400,
              body: file_fixture('kylas/deal_field_api_key_failed_response.json')
            )
            response = Kylas::FetchEntityFieldService.new(DEAL, user.tenant).fetch_field
            expect(response[:success]).to eq(false)
          end
        end
      end

      context "when get success response from field API" do
        it "should return success as true and valid fields data" do
          field_api_request(
            url: "#{API_URL}/#{API_VERSION}/deals/fields",
            status: 200,
            body: file_fixture('kylas/deal_field_success_response.json').read
          )
          response = Kylas::FetchEntityFieldService.new(DEAL, user.tenant).fetch_field
          expect(response[:success]).to eq(true)
        end
      end
    end

    context "when entity type is lead" do
      context "when get failed response from field API" do
        context "when api-key is invalid" do
          it "should return success as false" do
            field_api_request(
              url: "#{API_URL}/#{API_VERSION}/entities/lead/fields",
              status: 400,
              body: file_fixture('kylas/lead_field_api_key_failed_response.json')
            )
            response = Kylas::FetchEntityFieldService.new(LEAD, user.tenant).fetch_field
            expect(response[:success]).to eq(false)
          end
        end
      end

      context "when get success response from field API" do
        it "should return success as true and valid fields data" do
          field_api_request(
            url: "#{API_URL}/#{API_VERSION}/entities/lead/fields",
            status: 200,
            body: file_fixture('kylas/lead_field_success_response.json').read
          )
          response = Kylas::FetchEntityFieldService.new(LEAD, user.tenant).fetch_field
          expect(response[:success]).to eq(true)
        end
      end
    end

    context "when entity type is contact" do
      context "when get failed response from field API" do
        context "when api-key is invalid" do
          it "should return success as false" do
            field_api_request(
              url: "#{API_URL}/#{API_VERSION}/entities/contact/fields",
              status: 400,
              body: file_fixture('kylas/contact_field_api_key_failed_response.json')
            )
            response = Kylas::FetchEntityFieldService.new(CONTACT, user.tenant).fetch_field
            expect(response[:success]).to eq(false)
          end
        end
      end

      context "when get success response from field API" do
        it "should return success as true and valid fields data" do
          field_api_request(
            url: "#{API_URL}/#{API_VERSION}/entities/contact/fields",
            status: 200,
            body: file_fixture('kylas/contact_field_success_response.json').read
          )
          response = Kylas::FetchEntityFieldService.new(CONTACT, user.tenant).fetch_field
          expect(response[:success]).to eq(true)
        end
      end
    end
  end
end
