require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Kylas::FetchUserDetailService do
  let(:user){ create(:user) }
  let(:access_token) { "525801a0-9a90-43df-bd9c-f2682e5fdb45" }

  def fetch_request(status:, body:)
    stub_request(:get, KYLAS_ME_URL)
    .with(
      headers: { 'Authorization' => "Bearer #{access_token}" }
    ).to_return(status: status, body: body)
  end

  describe "#get_details" do
    context "when failed to fetch user details" do
      context "when user access token is blank" do
        it "should return success as false" do
          user.kylas_access_token = nil
          user_detail_response = Kylas::FetchUserDetailService.new(user).get_details
          expect(user_detail_response).to eq({ :success => false })
        end
      end

      context "when access token is invalid" do
        it "should return sucess as false with error message" do
          user.kylas_access_token = access_token
          fetch_request(
            status: 400,
            body: {
              code: "001002",
              message: "Uhoh! We are not able to recognize you. Please hit refresh and try again or contact support if you continue to encounter this error."
            }.to_json
          )
          user_detail_response = Kylas::FetchUserDetailService.new(user).get_details
          expect(user_detail_response).to eq({
            success: false,
            error: "Failed to get user details, please try again later Error Code: 001002 Error Description: Uhoh! We are not able to recognize you. Please hit refresh and try again or contact support if you continue to encounter this error."
          })
        end
      end
    end

    context "when user details fetched successfully" do
      it "should return the user details" do
        user.kylas_access_token = access_token
        fetch_request(
          status: 200,
          body: {
            createdAt: "2023-04-24T09:56:40.223+0000",
            updatedAt: "2023-04-24T09:57:23.682+0000",
            lastName: "Hardik Jade",
            email: "<EMAIL>",
            timezone: "Asia/Calcutta"
          }.to_json
        );

        user_detail_response = Kylas::FetchUserDetailService.new(user).get_details

        expect(user_detail_response).to eq(
          {
            :success => true,
            :details => {
                "createdAt" => "2023-04-24T09:56:40.223+0000",
                "updatedAt"=>"2023-04-24T09:57:23.682+0000",
                "lastName"=>"Hardik Jade",
                "email"=>"<EMAIL>",
                "timezone"=>"Asia/Calcutta"
              }
          }
        )
      end
    end
  end
end
