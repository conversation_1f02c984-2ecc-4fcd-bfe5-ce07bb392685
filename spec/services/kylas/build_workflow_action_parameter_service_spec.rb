require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Kylas::BuildWorkflowPayloadService do
  describe "#prepare" do
    let(:workflow_action) { build(:workflow_action) }
    let(:variables) {
      [
        {
          'name' => "name",
          'standard' => true
        }
      ]
    }

    context "when failed to prepare payload" do
      context "when workflow_action is blank" do

        it "should return the payload as nil" do
          payload = Kylas::BuildWorkflowPayloadService.new(nil, variables).prepare
          expect(payload).to eq(nil)
        end
      end
    end

    context "when payload prepared successfully" do

      it "should return valid payload" do
        workflow_action.id = 169
        workflow_action.tenant.webhook_api_key = 'f74963d0-ba09-48de-b055-41b104fb99f5'
        payload = Kylas::BuildWorkflowPayloadService.new(workflow_action, variables).prepare
        expect(payload).to eq(
          {
            "name" => "Send notification on teams",
            "description" => "This is used for sending automated notification in teams via workflow",
            "method"=>"POST",
            "resourceId" => "169",
            "requestUrl" => "https://localhost:3000/workflow-actions/f74963d0-ba09-48de-b055-41b104fb99f5/deliver-via-workflow.json",
            "parameters" => [
                {
                  "attribute"=>169,
                  "entity"=>"CUSTOM",
                  "isStandard"=>false,
                  "name"=>"resourceId"
                },
                {
                  "attribute"=>"id",
                  "entity"=>"DEAL",
                  "isStandard"=>true,
                  "name"=>"entityId"
                },
                {
                  "attribute"=>"id",
                  "entity"=>"CREATED_BY",
                  "isStandard"=>true,
                  "name"=>"record_created_by"
                },
                {
                  "attribute"=>"id",
                  "entity"=>"UPDATED_BY",
                  "isStandard"=>true,
                  "name"=>"record_updated_by"
                },
                {
                  "attribute"=>"name",
                  "entity"=>"DEAL",
                  "isStandard"=>true,
                  "name"=>"variable_name"
                },
                {
                  "attribute"=>"id",
                  "entity"=>"DEAL_OWNER",
                  "isStandard"=>true,
                  "name"=>"record_owner"
                }
              ]
            }
          )
      end
    end
  end
end
