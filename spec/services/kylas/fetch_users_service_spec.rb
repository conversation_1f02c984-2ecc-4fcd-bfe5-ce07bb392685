require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Kylas::FetchUsersService do
  describe '#fetch_users' do
    let(:user) { create(:user) }
    def fetch_users_request(status:, body:, page:)
      stub_request(:post, "#{KYLAS_USERS_URL}/search?page=#{page}&size=1000&sort=createdAt,asc").
        with(
          body: file_fixture('kylas/fetch_users_request_body.json').read.gsub(/[[:space:]]/, ''),
          headers: {
            'Content-Type'=>'application/json',
            'Authorization' => "Bearer #{user.get_kylas_access_token}"
          }
        ).to_return(status: status, body: body)
    end

    context 'when failed to fetch users' do
      context 'when access token is not present' do
        it 'should return success as false' do
          expect_any_instance_of(KylasEngine::User).to receive(:get_kylas_access_token).and_return(nil)
          response = Kylas::FetchUsersService.new(user).fetch_users
          expect(response[:success]).to eq(false)
        end
      end

      context 'when access token is expired/invalid' do
        it 'should return success as false' do
          fetch_users_request(status: 400, body: '{}', page: 0)
          response = Kylas::FetchUsersService.new(user).fetch_users
          expect(response[:success]).to eq(false)
        end
      end
    end

    context 'when user fetched successfully' do
      it 'should return success as true with list of all users' do
        fetch_users_request(status: 200, body: file_fixture('kylas/fetch_kylas_users_success_response.json').read, page: 0)
        response = Kylas::FetchUsersService.new(user).fetch_users
        expect(response[:success]).to eq(true)
      end
    end
  end
end
