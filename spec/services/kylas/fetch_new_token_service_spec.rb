require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Kylas::FetchTokenService do
  let(:user){ create(:user) }
  let(:refresh_token) { "525801a0-9a90-43df-bd9c-f2682e5fdb45" }

  def fetch_request(body:, status:)
    stub_request(:post, KYLAS_OAUTH_URL)
      .with(
        body: {
          'grant_type' => "refresh_token",
          'refresh_token' => refresh_token
        },
        headers: { 'Authorization' => 'Basic MTIzOjIzNA=='})
      .to_return(status: status, body: body)
  end

  describe '#get_token' do
    context "when failed to fetch token" do
      context "when user is blank" do
        it "should return success as false" do
          token_response = Kylas::FetchTokenService.new(nil).get_token
          expect(token_response).to eq({ success: false })
        end
      end

      context "when user's refresh token is blank" do
        it "should return success as false" do
          user.kylas_refresh_token = nil
          token_response = Kylas::FetchTokenService.new(user).get_token
          expect(token_response).to eq({ success: false })
        end
      end

      context "when refresh token is invalid" do
        before do
          allow(Rails.application.credentials.kylas).to receive(:client_id).and_return(123)
          allow(Rails.application.credentials.kylas).to receive(:client_secret).and_return(234)
        end

        it "should return success as false" do
          user.kylas_refresh_token = refresh_token
          fetch_request(body: "", status: 400)
          token_response = Kylas::FetchTokenService.new(user).get_token
          expect(token_response).to eq({ success: false })
        end
      end
    end

    context "when token fetched successfully" do
      before do
        allow(Rails.application.credentials.kylas).to receive(:client_id).and_return(123)
        allow(Rails.application.credentials.kylas).to receive(:client_secret).and_return(234)
      end

      it "should return new access token and refresh token" do
        user.kylas_refresh_token = refresh_token
        fetch_request(
          status: 200,
          body: {
            access_token: "************************************:3674:8084",
            refresh_token: refresh_token,
            expires_in: 86399
          }.to_json
        )
        token_response = Kylas::FetchTokenService.new(user).get_token

        expect(token_response[:success]).to eq(true)
        expect(token_response[:value][:kylas_access_token]).to eq('************************************:3674:8084')
        expect(token_response[:value][:kylas_refresh_token]).to eq(refresh_token)
      end
    end
  end
end
