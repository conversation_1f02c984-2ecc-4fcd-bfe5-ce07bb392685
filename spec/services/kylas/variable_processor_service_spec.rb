require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Kylas::VariableProcessorService do
  describe "#display_name_to_internal_name" do
    let(:entity_fields) {
      [
        {"displayName" => "Name", "name" => "name", "isStandard" => true},
        {"displayName" => "Forecasting Type", "name" => "forecastingType", "isStandard" => true}
      ]
    }
    let(:content_with_entity_field_and_fallback_text) { "{{Name}, {if missing: I am fallback text}}" }
    let(:content_with_entity_field_without_fallback_text) { "{{Name}, {if missing: <free text>}}" }
    let(:content_without_entity_field) { "Hello this is content" }

    context "when variables or entity_field is blank" do
      it "should not replace display name with internal name when entity_field is blank" do
        variables_and_content_hash = Kylas::VariableProcessorService.
          new(nil, content_with_entity_field_and_fallback_text)
          .display_name_to_internal_name
        expect(variables_and_content_hash[:content]).to eq(content_with_entity_field_and_fallback_text)
      end

      it "should not replace display name with internal name when content is not having any entity field" do
        variables_and_content_hash = Kylas::VariableProcessorService.
          new(entity_fields, content_without_entity_field)
          .display_name_to_internal_name
        expect(variables_and_content_hash[:content]).to eq(content_without_entity_field)
      end
    end

    context "when variable and entity_field is present" do
      it "should replace display name with internal name when fallback text is present" do
        variables_and_content_hash = Kylas::VariableProcessorService.
          new(entity_fields, content_with_entity_field_and_fallback_text)
          .display_name_to_internal_name
        expect(variables_and_content_hash[:content]).to eq("{{name}, {if missing: I am fallback text}}")
      end

      it "should replace display name with internal name when fallback text is missing" do
        variables_and_content_hash = Kylas::VariableProcessorService.
          new(entity_fields, content_with_entity_field_without_fallback_text)
          .display_name_to_internal_name
        expect(variables_and_content_hash[:content]).to eq("{{name}, {if missing: <free text>}}")
      end
    end
  end
end
