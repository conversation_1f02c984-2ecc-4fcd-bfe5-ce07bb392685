require 'rails_helper'
require 'webmock/rspec'

RSpec.describe CreateOrUpdateUser do
  let(:user) { create(:user, kylas_user_id: 8874) }
  let(:kylas_users) { JSON.parse(file_fixture('kylas/fetch_kylas_users_success_response.json').read)['content'] }

  describe '#process' do
    context 'when incorrect payload is passed' do
      it 'should return success as false if current user is nil' do
        response = CreateOrUpdateUser.new(nil).process(kylas_users)
        expect(response[:success]).to eq(false)
      end

      it 'should return success as false if users are empty' do
        response = CreateOrUpdateUser.new(user).process(nil)
        expect(response[:success]).to eq(false)
      end
    end

    context 'when correct payload is passed' do
      it 'should update the name of existing user if there is mismatch' do
        response = CreateOrUpdateUser.new(user).process(kylas_users)
        expect(response[:success]).to eq(true)
        expect(user.reload.email).to eq('<EMAIL>')
        expect(user.reload.name).to eq('<PERSON><PERSON>')
      end

      it 'should create the record if not found' do
        user.update(kylas_user_id: 1234)
        response = CreateOrUpdateUser.new(user).process(kylas_users)
        expect(response[:success]).to eq(true)
      end
    end
  end
end
