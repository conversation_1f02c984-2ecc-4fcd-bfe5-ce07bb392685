require 'rails_helper'

RSpec.describe SendNotificationService do
  let(:request_parameters) {
    {
      "api_key" => "b2d66331-ea0e-4173-b3e8-52f96e664ee9",
      "resourceId" => "146",
      "variable_ownedBy" => "Hardik Jade",
      "variable_company" => "Kylas Growth Engine",
      "entityId" => "58128",
      "variable_name" => "Deal for BMW Car"
    }.with_indifferent_access
  }

  let(:tenant) { create(:tenant, webhook_api_key: request_parameters[:api_key]) }
  let(:user) { create(:user, tenant_id: tenant.id) }
  let(:connected_account) { create(:connected_account, tenant_id: tenant.id, user_id: user.id) }
  let(:notification_template) { create(:notification_template, connected_account_id: connected_account.id) }
  let(:workflow_action) { create(:workflow_action, id: request_parameters[:resourceId],notification_template_id: notification_template.id, tenant_id: tenant.id, user_id: user.id) }
  let(:send_notification_service) { SendNotificationService.new(request_parameters) }

  describe "#process" do
    context "when incorrect payload is passed" do
      it "should return when api key is missing" do
        request_parameters[:api_key] = nil
        expect(Rails.logger).to receive(:error).with("API key is blank")
        send_notification_service.process
      end

      it "should return when api key is invalid" do
        request_parameters[:api_key] = 'ea0e-4173-b3e8-52f96e664ee9'
        expect(Rails.logger).to receive(:error).with("API key is not valid ea0e-4173-b3e8-52f96e664ee9")
        send_notification_service.process
      end

      it "should return when account is not connected" do
        tenant
        expect(Rails.logger).to receive(:error).with("Tenant has not connected an account or account is inactive #{tenant.id}")
        send_notification_service.process
      end

      it "should return when connected account is inactive" do
        connected_account.status = INACTIVE
        tenant.connected_account = connected_account
        expect(Rails.logger).to receive(:error).with("Tenant has not connected an account or account is inactive #{tenant.id}")
        send_notification_service.process
      end

      it "should return when entity id is missing" do
        tenant.connected_account = connected_account
        request_parameters[:entityId] = nil
        expect(Rails.logger).to receive(:error).with("Entity id or resource id is missing in payload entity id: , resource id: 146")
        send_notification_service.process
      end

      it "should return when resource id is missing" do
        tenant.connected_account = connected_account
        request_parameters[:resourceId] = nil
        expect(Rails.logger).to receive(:error).with("Entity id or resource id is missing in payload entity id: 58128, resource id: ")
        send_notification_service.process
      end

      it "should return when workflow action of tenant is missing" do
        tenant.connected_account = connected_account
        expect(Rails.logger).to receive(:error).with("Workflow action is not present for resourceId: 146")
        send_notification_service.process
      end
    end

    context "when correct payload is passed" do
      it "should call Teams::SendNotification#process action" do
        workflow_action
        tenant.connected_account = connected_account
        expect_any_instance_of(Teams::SendNotification).to receive(:process)
        send_notification_service.process
      end
    end
  end
end
