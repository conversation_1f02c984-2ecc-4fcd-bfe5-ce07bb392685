require 'rails_helper'
require 'webmock/rspec'

RSpec.describe ConnectedAccountService do
  let(:code){ "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyJ9" }

  def token_api_request(status:, body:)
    stub_request(:post, MICROSOFT_TOKEN_URL)
      .with(
        body: {
          "client_id" => Rails.application.credentials.notifier_app.client_id,
          "client_secret" => Rails.application.credentials.notifier_app.client_secret,
          "code" => code,
          "grant_type" => "authorization_code",
          "redirect_uri" => Rails.application.credentials.notifier_app.redirect_url,
          "scope" => MICROSOFT_AUTH_SCOPE
        },
        headers: {
          'Accept'=>'*/*',
          'Accept-Encoding'=>'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
          'Content-Type'=>'application/x-www-form-urlencoded',
          'Host'=>'login.microsoftonline.com',
          'User-Agent'=>'Ruby'
        }
      )
    .to_return(status: status, body: body)
  end

  let(:user) { create(:user) }
  let(:connected_account) { build(:connected_account, user: user, tenant: user.tenant) }
  let(:jwt_response) {[
    {
      "email" => "<EMAIL>",
      "name" => "Hardik Jade",
      "oid"=> 'iJKV1QiLCJub-eyJ0eXAiOiJKV1Qi-GciOiJSUzI1NiIsImtpZ'
    }
  ]}


  def get_failed_api_response(error, description, error_code)
    {
      "error"=> error,
      "error_description"=> description,
      "error_codes"=> [error_code],
      "timestamp"=> "2023-05-08 12:28:12Z",
      "trace_id"=> "bd7652dc-0688-498f-8f29-ed3edf228d00",
      "correlation_id"=> "1da4a46e-f8cd-4b3a-bb2a-e367a0a4a56e",
      "error_uri"=> "https://login.microsoftonline.com/error?code=#{error_code}"
    }
  end

  describe "#get_tokens" do
    context 'when get failed response from token API request' do
      context 'when client_id is invalid' do
        let(:invalid_response) {
          get_failed_api_response(
            'client_id',
            "AADSTS700016: Application with identifier '64d39a05e1c7-1227-4755-46ecb5f0' was not found in the directory '46ecb5f0-1227-4755-a101-64d39a05e1c7'. This can happen if the application has not been installed by the administrator of the tenant or consented to by any user in the tenant. You may have sent your authentication request to the wrong tenant.",
            700016
          )
        }

        it 'should return success as false' do
          token_api_request(
            status: 400,
            body: invalid_response.to_json
          )
          connected_account_service = ConnectedAccountService.new(connected_account, code)
          response = connected_account_service.get_token_and_save_account
          expect(response[:success]).to eq(false)
          expect(response[:error]).to eq("Failed to connect account, please try again later.Error Codes: 700016 AADSTS700016: Application with identifier '64d39a05e1c7-1227-4755-46ecb5f0' was not found in the directory '46ecb5f0-1227-4755-a101-64d39a05e1c7'. This can happen if the application has not been installed by the administrator of the tenant or consented to by any user in the tenant. You may have sent your authentication request to the wrong tenant.")
        end
      end

      context "when client secret is invalid" do
        let(:invalid_response) {
          get_failed_api_response(
            'invalid_client',
            "AADSTS7000215: Invalid client secret provided. Ensure the secret being sent in the request is the client secret value, not the client secret ID, for a secret added to app 'd2ab841d-f953-48e2-84ab-167e41e298f4'",
            7000215
          )
        }

        it 'should return success as false' do
          token_api_request(
            status: 400,
            body: invalid_response.to_json
          )
          connected_account_service = ConnectedAccountService.new(connected_account, code)
          response = connected_account_service.get_token_and_save_account
          expect(response[:success]).to eq(false)
          expect(response[:error]).to eq("Failed to connect account, please try again later.Error Codes: 7000215 AADSTS7000215: Invalid client secret provided. Ensure the secret being sent in the request is the client secret value, not the client secret ID, for a secret added to app 'd2ab841d-f953-48e2-84ab-167e41e298f4'")
        end
      end

      context "when code is invalid" do
        let(:invalid_response) {
          get_failed_api_response(
            'invalid_client',
            "AADSTS9002313: Invalid request. Request is malformed or invalid.",
            9002313
          )
        }

        it 'should return success as false' do
          token_api_request(
            status: 400,
            body: invalid_response.to_json
          )
          connected_account_service = ConnectedAccountService.new(connected_account, code)
          response = connected_account_service.get_token_and_save_account
          expect(response[:success]).to eq(false)
          expect(response[:error]).to eq('Failed to connect account, please try again later.Error Codes: 9002313 AADSTS9002313: Invalid request. Request is malformed or invalid.')
        end
      end

      context "when user reconnect with diffrent email address" do
        it "should return success as false with email mismatched error message" do
          token_api_request(
            status: 200,
            body: file_fixture('microsoft/token_success_response.json').read
          )

          expect(JWT).to receive(:decode).and_return(jwt_response)
          connected_account_service = ConnectedAccountService.new(connected_account, code)
          response = connected_account_service.get_token_and_save_account
          expect(response).to eq({
            :success => false,
            :error => "Email mismatched, Please login with same email address"
          })
        end
      end
    end

    context "when get success response from token API request" do
      context "when user has not connected an account" do
        it "should return success as true and save connected_account entry in database" do
          token_api_request(
            status: 200,
            body: file_fixture('microsoft/token_success_response.json').read
          )
          connected_account.email = nil
          expect(JWT).to receive(:decode).and_return(jwt_response)
          connected_account_service = ConnectedAccountService.new(connected_account, code)
          response = connected_account_service.get_token_and_save_account
          expect(response[:success]).to eq(true)
        end
      end

      context "when user has already connected an account" do
        it "should return success as true and save connected_account entry in database" do
          token_api_request(
            status: 200,
            body: file_fixture('microsoft/token_success_response.json').read
          )
          connected_account.save
          connected_account.email = nil
          expect(JWT).to receive(:decode).and_return(jwt_response)
          connected_account_service = ConnectedAccountService.new(connected_account, code)
          response = connected_account_service.get_token_and_save_account
          expect(response[:success]).to eq(true)
        end
      end
    end
  end
end
