require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Teams::FetchNewTokenService do
  let(:connected_account) { create(:connected_account) }
  let(:refresh_token) { "525801a0-9a90-43df-bd9c-f2682e5fdb45" }

  def fetch_token_request(status:, body:)
    stub_request(:post, MICROSOFT_TOKEN_URL)
    .with(
       body: {
        "client_id" => Rails.application.credentials.notifier_app.client_id,
        "client_secret" => Rails.application.credentials.notifier_app.client_secret,
        "grant_type" => "refresh_token",
        "refresh_token" => refresh_token
      },
      headers: { 'Content-Type'=>'application/x-www-form-urlencoded' }
    ).to_return(status: status, body: body)
  end

  describe "#get_token" do
    context "when get failed to get the access token" do
      context "when connected account is blank" do

        it "should return success as false" do
          token_service_response = Teams::FetchNewTokenService.new(nil).get_token
          expect(token_service_response[:success]).to eq(false)
        end
      end

      context "when refresh token is not present" do

        it "should return the success as true" do
          connected_account.refresh_token = nil
          token_service_response = Teams::FetchNewTokenService.new(connected_account).get_token
          expect(token_service_response[:success]).to eq(false)
        end
      end

      context "invalid refresh token is passed" do

        it "should return the success as false" do
          connected_account.refresh_token = refresh_token
          fetch_token_request(status: 400, body: "")
          token_service_response = Teams::FetchNewTokenService.new(connected_account).get_token
          expect(token_service_response[:success]).to eq(false)
        end
      end
    end

    context "when successfully get access token" do
      context "when valid refresh token is passed" do

        it "should return valid access token and refresh-token in response with success as true" do
          connected_account.refresh_token = refresh_token
          fetch_token_request(status: 200, body: file_fixture('microsoft/token_success_response.json'))
          token_service_response = Teams::FetchNewTokenService.new(connected_account).get_token
          expect(token_service_response[:success]).to eq(true)
          expect(token_service_response[:value][:access_token]).to eq('eyJ0eXAiOiJKV1QiLCJub25jZSI6InFUOEhlU2k4SVRZRHBmajB4WVFhT1p2UFkzN0UwOFJzNlU4QnNvVGpEU3ciLCJhbGciOiJSUzI1NiIsIng1dCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyIsImtpZCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyJ9')
          expect(token_service_response[:value][:refresh_token]).to eq('0.AVQA8LXsRicSVUehAWTTmgXhxx2Eq9JT-eJIhKsWfkHimPRUAAM.AgABAAEAAAD')
        end
      end
    end
  end
end
