require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Teams::FetchChannelService do
  let(:connected_account) { create(:connected_account) }

  def fetch_user_teams_request(status:, body:, token:)
    stub_request(:get, MICROSOFT_FETCH_TEAMS_URI)
    .with(headers: { 'Authorization' => "Bearer #{token}" })
    .to_return(status: status, body: body)
  end

  def fetch_channel_request(status:, body:, token:)
    stub_request(:get, "#{MICROSOFT_CHANNEL_BASE_URI}/7db7f8ab133e-cd8f-9041-4f1b/channels")
    .with(headers: { 'Authorization' => "Bearer #{token}" })
    .to_return(status: status, body: body)
  end

  describe "#fetch_channels" do
    context "when failed to get the channels" do
      context "when failed to get an access token" do
        it "should return the connect kylas support failure message" do
          expect_any_instance_of(ConnectedAccount).to receive(:get_access_token).and_return(nil)
          expect(Teams::FetchChannelService.new(connected_account).fetch_channels).to eq({
            :success => false,
            :error => "Something went wrong. Please try again later or contact <PERSON>yla<PERSON> support"
          })
        end
      end

      context "when failed to get an user teams" do
        context "when access token is invalid" do
          it "should return the failure message that access token is not valid" do
            fetch_user_teams_request(
              status: 400,
              body: {
                "error" => {
                  "code" => "InvalidAuthenticationToken",
                  "message" => "CompactToken parsing failed with error code: ********",
                  "innerError" => {
                    "date" => "2023-06-14T15:29:47",
                    "request-id" => "5e076716-661d-48ac-b933-be5814402569",
                    "client-request-id" => "5e076716-661d-48ac-b933-be5814402569"
                  }
                }
              }.to_json,
              token: connected_account.get_access_token
            )
            expect(Teams::FetchChannelService.new(connected_account).fetch_channels).to eq({
              :success => false,
              :error => "Failed to fetch channels, Please try again later. Error Code: InvalidAuthenticationToken, Error Description: CompactToken parsing failed with error code: ********"
            })
          end
        end

        context "when failed to parse response json body" do
          it "should return the error message" do
            fetch_user_teams_request(
              status: 400,
              body: "",
              token: connected_account.get_access_token
            )
            expect(Teams::FetchChannelService.new(connected_account).fetch_channels).to eq({:success=>false, :error=>"unexpected token at ''"})
          end
        end
      end

      context "when failed to get channel of the joined teams" do
        context "when team id is invalid" do
          it "should return the fetch channel failure message" do
            fetch_user_teams_request(
              status: 200,
              body: {
                "@odata.context" => "https://graph.microsoft.com/v1.0/$metadata#teams",
                "@odata.count" => 1,
                "value" => [
                  {
                    "id" => "7db7f8ab133e-cd8f-9041-4f1b",
                    "createdDateTime" => nil,
                    "displayName" => "Everyone",
                    "description" => "",
                    "internalId" => nil,
                    "classification" => nil,
                    "specialization" => nil,
                    "visibility" => nil,
                    "webUrl" => nil,
                    "isArchived" => false,
                    "tenantId" => "46ecb5f0-1227-4755-a101",
                    "isMembershipLimitedToOwners" => nil,
                    "memberSettings" => nil,
                    "guestSettings" => nil,
                    "messagingSettings" => nil,
                    "funSettings" => nil,
                    "discoverySettings" => nil,
                    "summary" => nil
                  }
                ]
              }.to_json,
              token: connected_account.get_access_token
            )
            fetch_channel_request(
              status: 400,
              body: {
                :success => false,
                :error => "Failed to fetch channels, please try again later Error Code: BadRequest Error Description: teamId needs to be a valid GUID."
              }.to_json,
              token: connected_account.get_access_token
            )
            expect(Teams::FetchChannelService.new(connected_account).fetch_channels).to eq(
              {
                :success => false,
                :error => "Failed to fetch channels, Please try again later."
              }
            )
          end
        end
      end
    end

    context "when successfully get channels" do
      it "should return the user channel with respective teams" do
        fetch_user_teams_request(
          status: 200,
          body: {
            "@odata.context" => "https://graph.microsoft.com/v1.0/$metadata#teams",
            "@odata.count" => 1,
            "value" => [
              {
                "id" => "7db7f8ab133e-cd8f-9041-4f1b",
                "createdDateTime" => nil,
                "displayName" => "Everyone",
                "description" => "",
                "internalId" => nil,
                "classification" => nil,
                "specialization" => nil,
                "visibility" => nil,
                "webUrl" => nil,
                "isArchived" => false,
                "tenantId" => "46ecb5f0-1227-4755-a101",
                "isMembershipLimitedToOwners" => nil,
                "memberSettings" => nil,
                "guestSettings" => nil,
                "messagingSettings" => nil,
                "funSettings" => nil,
                "discoverySettings" => nil,
                "summary" => nil
              }
            ]
          }.to_json,
          token: connected_account.get_access_token
        )
        fetch_channel_request(
          status: 200,
          body: {
            "@odata.context" => "https://graph.microsoft.com/v1.0/$metadata#teams('d0be242c-cd8f-4f1b-9041-7db7f8ab133e')/channels",
            "@odata.count" => 1,
            "value" => [
              {
                "id" => "19:27av4b333d23935331f35f2gf26csse@thread.tacv2",
                "createdDateTime" => "2020-12-09T13:35:23.717Z",
                "displayName" => "General",
                "description" => "",
                "isFavoriteByDefault" => nil,
                "email" => "<EMAIL>",
                "tenantId" => "46ecb5f0-1227-4755-a101",
                "webUrl" => "https://teams.microsoft.com/l/channel/19%3A27aa7b3222d2493581f25f2cf3f26cbe%40thread.tacv2/Everyone?groupId=d0be242c-cd8f-4f1b-9041-7db7f8ab133e&tenantId=46ecb5f0-1227-4755-a101-64d39a05e1c7&allowXTenantAccess=False",
                "membershipType" => "standard"
              }
            ]
          }.to_json,
          token: connected_account.get_access_token
        )
        expect(Teams::FetchChannelService.new(connected_account).fetch_channels).to eq(
          {
            :success => true,
            :channel => [
              [
                "Team - Everyone",
                [
                  [
                    "General",
                    "19:27av4b333d23935331f35f2gf26csse@thread.tacv2",
                    { "team-id" => "7db7f8ab133e-cd8f-9041-4f1b", 'recipient-type' => CHANNEL }
                  ]
                ]
              ]
            ]
          }
        )
      end
    end
  end
end
