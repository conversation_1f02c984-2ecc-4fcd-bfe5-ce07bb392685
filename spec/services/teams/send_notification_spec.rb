require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Teams::SendNotification do
  let(:connected_account){ create(:connected_account) }
  let(:notification_template) { create(:notification_template, connected_account: connected_account) }
  let(:workflow_action) { create(:workflow_action, notification_template_id: notification_template.id, tenant_id: notification_template.connected_account.tenant_id, user_id: notification_template.connected_account.user_id) }
  let(:notifier) { create(:notifier, notification_template_id: notification_template.id) }

  let(:request_parameters) {
    {
      "subject" => template_subject,
      "importance" => notification_template.message_type,
      "body" => {
        "contentType" => "html",
        "content" => template_content + "<hr/><p style='font-size: 12px; color: #687790;'>Sent by <PERSON><PERSON>s Notifier</p>"
      },
      "mentions" => [
          {
            "id": 0,
            "mentionText": "John Doe",
            "mentioned": {
              "user": { "displayName": "John Doe", "id": "6e6dc82e-83e8-b53be739dc87" }
            }
          }
        ]
    }
  }

  let(:invalid_token_response){
    {
      "error" => {
        "code"=>"InvalidAuthenticationToken",
        "message" => "CompactToken parsing failed with error code: ********",
        "innerError" => {
          "date" => "2023-06-07T06:20:38",
          "request-id" => "9eab25d3-6ea3-4654-abd7-60e656c091a9",
          "client-request-id" => "9eab25d3-6ea3-4654-abd7-60e656c091a9"
        }
      }
    }
  }

  def stub_send_notification_request(status:, body:)
    stub_request(:post, "https://graph.microsoft.com/v1.0/teams/#{notifier.get_team_or_chat_id}/channels/#{notifier.recipient_id}/messages")
      .with(
        body: request_parameters.to_json,
        headers: { 'Authorization' => "Bearer #{notification_template.connected_account.access_token}" }
      )
      .to_return(status: status, body: body)
  end

  def stub_send_notification_to_user_request(options = {}, status:, body:)
    stub_request(:post, "#{MICROSOFT_CHAT_URI}/#{notifier.get_team_or_chat_id(options)}/messages")
      .with(
        body: request_parameters.to_json,
        headers: { 'Authorization' => "Bearer #{notification_template.connected_account.access_token}" }
      )
      .to_return(status: status, body: body)
  end

  def send_notification_service(entity_request_parameters:)
    Teams::SendNotification.new(
      notification_template.connected_account.tenant,
      workflow_action,
      entity_request_parameters
    ).process
  end

  describe "#process" do
    context "when recieved failure status code" do
      let(:template_content){ "please have a look for lead <at id=0>John Doe</at>" }
      let(:template_subject){ "Sample Heading" }
      before do
        notification_template.update(content: "please have a look for lead [[@John Doe&lt;!-- [[user|*|6e6dc82e-83e8-b53be739dc87]] --&gt;]]", heading: 'Sample Heading')
        stub_send_notification_request(
          status: 401,
          body: file_fixture('microsoft/invalid_token_response.json')
        )
        expect(Rails.logger).to receive(:error).with("Teams::SendNotification: send: Errors -> #{invalid_token_response}")
      end

      it "should log error message when bearer token is invalid" do
        expect do
          send_notification_service(entity_request_parameters: {})
        end.to change(NotificationLog, :count).by(1)
        expect(NotificationLog.last.notification_content).to eq(template_content)
      end
    end

    context "when recieved success status code" do
      context "when entity type is deal" do
        let(:template_content){ 'Kyla Pittman deal <at id=0>John Doe</at>' }
        let(:template_subject){ 'Sample Heading: Kyla Pittman' }
        before {
          notification_template.update(content: '{{name}, {if missing: Deal Name}} {{products}, {if missing: <free text>}} [[@John Doe&lt;!-- [[user|*|6e6dc82e-83e8-b53be739dc87]] --&gt;]]', heading: 'Sample Heading: {{name}, {if missing: <free text>}}')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'products', variable_type: 'LOOK_UP')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'name', variable_type: 'TEXT_FIELD')
        }

        it "should send notification to channel and return when bearer token correct" do
          stub_send_notification_request(
            status: 201,
            body: file_fixture('teams/deal_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/deal_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to user and return when bearer token correct" do
          stub_send_notification_to_user_request(
            status: 201,
            body: file_fixture('teams/deal_notification_send_success_response.json')
          )
          notifier.update(recipient_type: USER)
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/deal_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record owner and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_owner' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/deal_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/deal_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record created_by and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_updated_by' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/deal_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/deal_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record updated_by and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_created_by' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/deal_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/deal_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end
      end

      context "when entity type is lead" do
        let(:template_content){ 'Hardik Jade 10-19 +************, +************ <at id=0>John Doe</at>' }
        let(:template_subject){ 'Sample Heading: Hardik Jade' }
        before{
          notification_template.update(content: '{{ownerId}, {if missing: Lead Name}} {{companyEmployees}, {if missing: 15-20}} {{phoneNumbers}, {if missing: ************}} [[@John Doe&lt;!-- [[user|*|6e6dc82e-83e8-b53be739dc87]] --&gt;]]', heading: 'Sample Heading: {{ownerId}, {if missing: Lead Name}}')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'ownerId', variable_type: 'LOOK_UP')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'companyEmployees', variable_type: 'PICK_LIST')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'phoneNumbers', variable_type: 'PHONE')
        }

        it "should send notification to channel and return when bearer token correct" do
          stub_send_notification_request(
            status: 201,
            body: file_fixture('teams/lead_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/lead_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to user and return when bearer token correct" do
          stub_send_notification_to_user_request(
            status: 201,
            body: file_fixture('teams/lead_notification_send_success_response.json')
          )
          notifier.update(recipient_type: USER)
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/lead_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record owner and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_owner' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/lead_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/lead_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record created_by and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_updated_by' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/lead_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/lead_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record updated_by and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_created_by' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/lead_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/lead_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end
      end

      context "when entity is contact" do
        let(:template_content){ 'Beau <EMAIL>, <EMAIL> +************, +************ <at id=0>John Doe</at>' }
        let(:template_subject){ 'Sample Heading: Beau' }
        before {
          notification_template.update(content: '{{firstName}, {if missing: First Name}} {{emails}, {if missing: 15-20}} {{phoneNumbers}, {if missing: ************}} [[@John Doe&lt;!-- [[user|*|6e6dc82e-83e8-b53be739dc87]] --&gt;]]', heading: 'Sample Heading: {{firstName}, {if missing: First Name}}')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'firstName', variable_type: 'TEXT_FIELD')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'emails', variable_type: 'EMAIL')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'phoneNumbers', variable_type: 'PHONE')
        }

        it "should send notification to channel and return when bearer token correct" do
          stub_send_notification_request(
            status: 201,
            body: file_fixture('teams/contact_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/contact_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to user and return when bearer token correct" do
          stub_send_notification_to_user_request(
            status: 201,
            body: file_fixture('teams/contact_notification_send_success_response.json')
          )
          notifier.update(recipient_type: USER)
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/contact_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record owner and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_owner' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/contact_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/contact_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record created_by and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_updated_by' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/contact_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/contact_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record updated_by and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_created_by' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/contact_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/contact_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end
      end

      context "when entity is meeting" do
        let(:template_content){ "Hardik Jade Hardik Jade <at id=0>John Doe</at>" }
        let(:template_subject){ 'Sample Heading: Hardik Jade' }
        before {
          notification_template.update(content: '{{owner}, {if missing: Hardik Jade}} {{organizer}, {if missing: Organizer}} [[@John Doe&lt;!-- [[user|*|6e6dc82e-83e8-b53be739dc87]] --&gt;]]', heading: 'Sample Heading: {{owner}, {if missing: Hardik Jade}}')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'owner', variable_type: 'LOOK_UP')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'organizer', variable_type: 'MEETING_ORGANIZER')
        }

        it "should send notification to channel and return when bearer token correct" do
          stub_send_notification_request(
            status: 201,
            body: file_fixture('teams/meeting_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/meeting_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to user and return when bearer token correct" do
          stub_send_notification_to_user_request(
            status: 201,
            body: file_fixture('teams/meeting_notification_send_success_response.json')
          )
          notifier.update(recipient_type: USER)
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/meeting_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record owner and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_owner' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/meeting_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/meeting_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record created_by and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_updated_by' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/meeting_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/meeting_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record updated_by and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_created_by' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/meeting_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/meeting_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end
      end

      context "when entity is call log" do
        let(:template_content){ 'Hardik Jade OUTGOING 57 <at id=0>John Doe</at>' }
        let(:template_subject){ 'Sample Heading: Hardik Jade' }
        before {
          notification_template.update(content: '{{owner}, {if missing: Hardik Jade}} {{callType}, {if missing: OUTGOING}} {{duration}, {if missing: duration}} [[@John Doe&lt;!-- [[user|*|6e6dc82e-83e8-b53be739dc87]] --&gt;]]', heading: 'Sample Heading: {{owner}, {if missing: Hardik Jade}}')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'owner', variable_type: 'TEXT_FIELD')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'callType', variable_type: 'ENTITY_PICKLIST')
          create(:variable_type_mapping, notification_template_id: notification_template.id, name: 'duration', variable_type: 'NUMBER')
        }

        it "should send notification to channel and return when bearer token correct" do
          stub_send_notification_request(
            status: 201,
            body: file_fixture('teams/call_log_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/call_log_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to user and return when bearer token correct" do
          stub_send_notification_to_user_request(
            status: 201,
            body: file_fixture('teams/call_log_notification_send_success_response.json')
          )
          notifier.update(recipient_type: USER)
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/call_log_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record owner and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_owner' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/call_log_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/call_log_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record created_by and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_updated_by' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/call_log_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/call_log_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end

        it "should send notification to relative record updated_by and return when bearer token correct" do
          notifier.update({ recipient_type: RELATIVE, recipient_id: 'record_created_by' })

          stub_send_notification_to_user_request(
            { record_owner: connected_account.user.kylas_user_id },
            status: 201,
            body: file_fixture('teams/call_log_notification_send_success_response.json')
          )
          expect do
            send_notification_service(entity_request_parameters: JSON.parse(file_fixture('teams/call_log_request_params.json').read))
          end.to change(NotificationLog, :count).by(1)
          expect(NotificationLog.last.notification_content).to eq(template_content)
        end
      end
    end
  end
end
