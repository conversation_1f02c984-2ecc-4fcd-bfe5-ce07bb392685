require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Teams::FetchUsersService do
  let(:connected_account) { create(:connected_account) }
  let(:notification_template){ build(:notification_template) }
  let(:notifier){ build(:notifier, notification_template_id: notification_template.id) }

  let(:failure_response) do
    {
      error: {
        code: '7865',
        message: 'Something went wrong!'
      }
    }
  end

  let(:valid_users_fetch_response) do
    {
      value: [
        {
          displayName: "<PERSON> Doe",
          givenName: "<PERSON>",
          mail: "<EMAIL>",
          surname: "<PERSON><PERSON>",
          userPrincipalName: "<EMAIL>",
          id: "9561-3447-49c4-589c2289-f0f6"
        }
      ]
    }
  end

  let(:failure_and_contact_response) do
    {
      success: false,
      error: "Something went wrong. Please try again later or contact <PERSON><PERSON><PERSON> support"
    }
  end

  let(:expected_error_response) do
    {
      success: false,
      error: "Failed to fetch users, please try again later Error Code: 7865 Error Description: Something went wrong!"
    }
  end

  let(:fetch_users_success_response) do
    {
      success: true,
      users: [ ["Users", [ ["<PERSON> (<EMAIL>)", "9561-3447-49c4-589c2289-f0f6", { 'recipient-type': "user", name: 'John Doe' }] ]] ]
    }
  end

  let(:chat_id_success_response) do
    {
      "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#chats/$entity",
      id: "19:<EMAIL>",
      createdDateTime: "2023-07-07T06:22:09.421Z",
      lastUpdatedDateTime: "2023-07-07T06:22:09.421Z",
      chatType: "oneOnOne",
      tenantId: "46ecb5f0-a101-4755-69q35a25e1c7"
    }
  end

  let(:query_missing_error) do
    {
      success: false,
      error: "Failed to fetch users, query parameters is missing."
    }
  end

  def fetch_users_request(skip_token = nil,status:, body:, token:)
    url = "https://graph.microsoft.com/v1.0/users"
    url += "?$skiptoken=" + skip_token if skip_token.present?
    stub_request(:get, url)
    .with(headers: { 'Authorization' => "Bearer #{token}" })
    .to_return(status: status, body: body)
  end

  def get_chat_id_request(status:, body:, token:)
    stub_request(:post, MICROSOFT_CHAT_URI)
    .with(
      headers: { 'Authorization' => "Bearer #{token}" },
      body: {
        chatType: "oneOnOne",
        members: [
          {
            "@odata.type": "#microsoft.graph.aadUserConversationMember",
            roles: [ "owner" ],
            "<EMAIL>": "https://graph.microsoft.com/v1.0/users/#{connected_account.microsoft_user_id}"
          },
          {
            "@odata.type": "#microsoft.graph.aadUserConversationMember",
            roles: [ "owner" ],
            "<EMAIL>": "https://graph.microsoft.com/v1.0/users/#{notifier.recipient_id}"
          }
        ]
      }
    )
    .to_return(status: status, body: body)
  end

  def fetch_teams_users_request(status:, body:, token:, query:)
    stub_request(:get, "#{MICROSOFT_FETCH_USERS_URI}?$search=\"displayName:#{query}\"")
    .with(headers: {
      'Authorization' => "Bearer #{token}",
      'ConsistencyLevel' => "eventual"
    })
    .to_return(status: status, body: body)
  end

  describe "#get_users" do
    context "when failed to fetch users" do
      context "when failed to get an access token" do
        it "should returns the error message" do
          expect_any_instance_of(ConnectedAccount).to receive(:get_access_token).and_return(nil)
          expect(Teams::FetchUsersService.new(connected_account).get_users).to eq(failure_and_contact_response)
        end
      end

      context "when response code is not 200" do
        it "should returns the error message" do
          fetch_users_request(
            status: 400,
            body: failure_response.to_json,
            token: connected_account.access_token
          )
          expect(Teams::FetchUsersService.new(connected_account).get_users).to eq(expected_error_response)
        end
      end
    end

    context "when users fetched successfully" do
      it "should return the users list with respective id" do
        fetch_users_request(
          status: 200,
          body: valid_users_fetch_response.to_json,
          token: connected_account.access_token
        )
        expect(Teams::FetchUsersService.new(connected_account).get_users).to eq(fetch_users_success_response)
      end
    end
  end

  describe "#get_all_users" do
    context "when failed to fetch users" do
      context "when failed to get an access token" do
        it "should returns the error message" do
          expect_any_instance_of(ConnectedAccount).to receive(:get_access_token).and_return(nil)
          response = Teams::FetchUsersService.new(connected_account).get_all_users
          expect(response[:success]).to eq(false)
        end
      end

      context "when response code is not 200" do
        it "should returns the error message" do
          fetch_users_request(
            status: 400,
            body: failure_response.to_json,
            token: connected_account.access_token
          )
          response = Teams::FetchUsersService.new(connected_account).get_all_users
          expect(response[:success]).to eq(false)
        end
      end
    end

    context "when users fetched successfully" do
      it "should returns the list of users with success as true" do
        fetch_users_request(
          status: 200,
          body: file_fixture('teams/get_all_users_success_response.json'),
          token: connected_account.access_token
        )

        fetch_users_request(
          "OS04ZTg2LTQ0NTAtYmIwOC05M2U2ZGQxMGYwY2K5AAAAAAAAAAAAAA",
          status: 200,
          body: file_fixture('teams/get_all_users_final_success_response.json'),
          token: connected_account.access_token
        )

        response = Teams::FetchUsersService.new(connected_account).get_all_users
        expect(response[:success]).to eq(true)
        expect(response[:data].map{ |user| user['mail'] }.join(', ')).to eq("<EMAIL>, <EMAIL>")
      end
    end
  end

  describe "#get_chat_id" do
    context "when failed to get chat id" do
      context "when failed to get an access token" do
        it "should returns the error message" do
          expect_any_instance_of(ConnectedAccount).to receive(:get_access_token).and_return(nil)
          expect(Teams::FetchUsersService.new(connected_account, notification_template).get_chat_id(notifier.recipient_id)).to eq(failure_and_contact_response)
        end
      end

      context "when response code is not 201" do
        it "should returns the error message" do
          get_chat_id_request(
            status: 400,
            body: failure_response.to_json,
            token: connected_account.access_token
          )

          expect(Teams::FetchUsersService.new(connected_account, notification_template).get_chat_id(notifier.recipient_id)).to eq(expected_error_response)
        end
      end
    end

    context "when chat id fetched successfully" do
      it "should return the success as true with chat id" do
        get_chat_id_request(
          status: 201,
          body: chat_id_success_response.to_json,
          token: connected_account.access_token
        )

        expect(Teams::FetchUsersService.new(connected_account, notification_template).get_chat_id(notifier.recipient_id)).to eq(
          {
            success: true,
            chat_id: "19:<EMAIL>"
          }
        )
      end
    end
  end

  describe "#fetch_teams_users_by_name" do
    context "when failed to fetch users" do
      context "when query is missing" do
        it "should return the success as false with query missing error message" do
          expect(Teams::FetchUsersService.new(connected_account).fetch_users_by_name(nil))
            .to eq(query_missing_error)
        end
      end

      context "when failed to get an access token" do
        it "should return the failure error message" do
          expect_any_instance_of(ConnectedAccount).to receive(:get_access_token).and_return(nil)
          expect(Teams::FetchUsersService.new(connected_account).fetch_users_by_name('Har'))
            .to eq(failure_and_contact_response)
        end
      end
    end

    context "when users fetched successfully" do
      it "should return the users list with respective id" do
        fetch_teams_users_request(
          status: 200,
          body: valid_users_fetch_response.to_json,
          token: connected_account.access_token,
          query: 'Har'
        )
        expect(Teams::FetchUsersService.new(connected_account).fetch_users_by_name('Har'))
          .to eq(fetch_users_success_response)
      end
    end
  end
end
