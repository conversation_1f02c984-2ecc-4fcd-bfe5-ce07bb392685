require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Teams::TemplatePayload do
  let(:connected_account) { create(:connected_account) }
  let(:fetch_users_success){
    {
      success: true,
      users: [
        [
          "Users",
          [
            [
              "<PERSON> (<EMAIL>)",
              "3447-49c4-9561-90fff0f6-c3bd589c2289",
              {:"recipient-type"=>"user", :name=>"<PERSON>"}
            ]
          ]
        ]
      ]
    }
  }

  let(:fetch_channels_success){
    {
      success: true,
      channel: [
        [
          "Channel - Kylas",
          [
            [
              "Monitoring-Apps",
              "19:<EMAIL>",
              { "team-id" => "0d4c6-1bd7-2f0e89-43dd", "recipient-type"=>"channel" }
            ]
          ]
        ]
      ]
    }
  }

  let(:success_response){
    {
      success: true,
      data: [
              [
                "Channel - Kylas",
                [
                  [
                    "Monitoring-Apps",
                    "channel|*|Monitoring-Apps|*|19:<EMAIL>|*|0d4c6-1bd7-2f0e89-43dd"
                  ]
                ]
              ],
              [
                "Users",
                [
                  ["<PERSON> Doe (<EMAIL>)", "user|*|John Doe (<EMAIL>)|*|3447-49c4-9561-90fff0f6-c3bd589c2289"]
                ]
              ]
            ]
    }
  }

  describe '#prepare' do
    context 'when success response is false' do
      context 'when failed to fetch teams channels' do
        it 'should return success as false with error message' do
          expect_any_instance_of(Teams::FetchChannelService).to receive(:fetch_channels).and_return({
            success: false,
            error: 'Error in fetching channels'
          })

          expect(described_class.new(connected_account).prepare).to eq({
            success: false,
            message: 'Error in fetching channels'
          })
        end
      end

      context 'when failed to fetch teams users' do
        it 'should return success as false with error message' do
          expect_any_instance_of(Teams::FetchChannelService).to receive(:fetch_channels).and_return({ success: true })
          expect_any_instance_of(Teams::FetchUsersService).to receive(:get_users).and_return({
            success: false,
            error: 'Error in fetching users'
          })

          expect(described_class.new(connected_account).prepare).to eq({
            success: false,
            message: 'Error in fetching users'
          })
        end
      end
    end

    context 'when success response is true' do
      context 'when teams channels and users fetched successfully' do
        it 'should return success as true with users data' do
          expect_any_instance_of(Teams::FetchChannelService).to receive(:fetch_channels).and_return(fetch_channels_success)
          expect_any_instance_of(Teams::FetchUsersService).to receive(:get_users).and_return(fetch_users_success)

          expect(described_class.new(connected_account).prepare).to eq(success_response)
        end
      end
    end
  end

  describe '#teams_user_by_name' do
    context 'when success response is false' do
      context 'when failed to fetch teams users' do
        it 'should return success as false with error message' do
          expect_any_instance_of(Teams::FetchUsersService).to receive(:fetch_users_by_name).and_return({
            success: false,
            error: 'Error in fetching users'
          })

          expect(described_class.new(connected_account).teams_user_by_name('bogambo')).to eq({
            success: false,
            message: 'Error in fetching users'
          })
        end
      end
    end

    context 'when success response is true' do
      it 'should return success as true with teams users' do
        expect_any_instance_of(Teams::FetchUsersService).to receive(:fetch_users_by_name).and_return(fetch_users_success)
        expect(described_class.new(connected_account).teams_user_by_name('bogambo')).to eq(
          {
            success: true,
            users: [
              ["Users",
                [
                  [
                    "John Doe (<EMAIL>)",
                    "user|*|John Doe (<EMAIL>)|*|3447-49c4-9561-90fff0f6-c3bd589c2289"
                  ]
                ]
              ]
            ]
          }
        )
      end
    end
  end

  describe '#mention_users' do
    context 'when success response is false' do
      context 'when failed to fetch teams users' do
        before do
          expect_any_instance_of(Teams::FetchUsersService).to receive(:fetch_users_by_name).and_return({
            success: false,
            error: 'Error in fetching users'
          })
        end

        it 'should return success as false with error message' do
          expect(described_class.new(connected_account).mention_users('bogambo')).to eq({
            success: false,
            message: 'Error in fetching users'
          })
        end
      end
    end

    context 'when success response is true' do
      before do
        expect_any_instance_of(Teams::FetchUsersService).to receive(:fetch_users_by_name).and_return(fetch_users_success)
      end

      it 'should return success as true with teams users' do
        expect(described_class.new(connected_account).mention_users('bogambo')).to eq({
            success: true,
            users: [
              {
                id: "[[@John Doe<!-- [[user|*|3447-49c4-9561-90fff0f6-c3bd589c2289]] -->]]",
                text: "John Doe (<EMAIL>)"
              }
            ]
          })
      end
    end
  end
end
