# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Teams::SyncAgentMappingsJob, :job do
  describe '#perform' do
    let(:connected_account) { create(:connected_account) }
    let(:job_id){ '791b98dc-ff1b-4aa0-9a95-57956ef09143' }

    context 'when users are fetched successfully' do
      before do
        connected_account.tenant.update(agent_mapping_job: job_id)
        expect_any_instance_of(Teams::FetchUsersService).to receive(:get_unmapped_users).and_return({
            :success => true,
            :data => []
          })
      end

      it 'resets agent_mapping_job' do
        expect { described_class.new.perform(connected_account.id) }
        .to change{ connected_account.reload.tenant.agent_mapping_job }
        .from(job_id)
        .to(nil)
      end
    end
  end
end
