[{"id": 118549, "type": "TEXT_FIELD", "name": "name", "displayName": "Name", "description": null, "sortable": true, "filterable": true, "required": true, "active": true, "standard": true, "min": 3, "max": 255, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118550, "type": "FORECASTING_TYPE", "name": "forecastingType", "displayName": "Forecasting Type", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118551, "type": "LOOK_UP", "name": "ownedBy", "displayName": "Owner", "description": null, "sortable": false, "filterable": true, "required": true, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118552, "type": "MONEY", "name": "estimatedValue", "displayName": "Estimated Value", "description": null, "sortable": true, "filterable": true, "required": true, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118553, "type": "MONEY", "name": "actualValue", "displayName": "Actual Value", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118554, "type": "DATE_PICKER", "name": "estimatedClosureOn", "displayName": "Estimated Closure Date", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118556, "type": "LOOK_UP", "name": "company", "displayName": "Company", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118557, "type": "PIPELINE", "name": "pipeline", "displayName": "Pipeline", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118558, "type": "PIPELINE_STAGE", "name": "pipelineStage", "displayName": "Pipeline Stage", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118559, "type": "LOOK_UP", "name": "associatedContacts", "displayName": "Contacts", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118560, "type": "LOOK_UP", "name": "created<PERSON>y", "displayName": "Created By", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118561, "type": "DATETIME_PICKER", "name": "createdAt", "displayName": "Created At", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118562, "type": "LOOK_UP", "name": "updatedBy", "displayName": "Updated By", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118563, "type": "DATETIME_PICKER", "name": "updatedAt", "displayName": "Updated At", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118564, "type": "DATETIME_PICKER", "name": "actualClosureDate", "displayName": "Actual Closure Date", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118565, "type": "TEXT_FIELD", "name": "pipelineStageReason", "displayName": "Pipeline Stage Reason", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118566, "type": "DATETIME_PICKER", "name": "taskDueOn", "displayName": "Task Due On", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118567, "type": "DATETIME_PICKER", "name": "meetingScheduledOn", "displayName": "Meeting Scheduled On", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118568, "type": "DATETIME_PICKER", "name": "latestActivityCreatedAt", "displayName": "Latest Activity On", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118569, "type": "TOGGLE", "name": "isNew", "displayName": "Is New", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118570, "type": "PICK_LIST", "name": "campaign", "displayName": "Campaign", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": {"id": 4264, "name": "CAMPAIGN", "picklistValues": [{"id": 13222, "name": "ORGANIC", "displayName": "Organic", "disabled": false}]}}, {"id": 118571, "type": "PICK_LIST", "name": "source", "displayName": "Source", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": {"id": 4265, "name": "SOURCE", "picklistValues": [{"id": 13223, "name": "GOOGLE", "displayName": "Google", "disabled": false}, {"id": 13224, "name": "FACEBOOK", "displayName": "Facebook", "disabled": false}, {"id": 13225, "name": "LINKEDIN", "displayName": "LinkedIn", "disabled": false}, {"id": 13226, "name": "EXHIBITION", "displayName": "Exhibition", "disabled": false}, {"id": 13227, "name": "COLD_CALLING", "displayName": "Cold Calling", "disabled": false}]}}, {"id": 118572, "type": "LOOK_UP", "name": "products", "displayName": "Product or Service", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118573, "type": "NUMBER", "name": "quantity", "displayName": "Quantity", "description": null, "sortable": false, "filterable": false, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118574, "type": "MONEY", "name": "price", "displayName": "Price", "description": null, "sortable": false, "filterable": false, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118575, "type": "DISCOUNT", "name": "discount", "displayName": "Discount", "description": null, "sortable": false, "filterable": false, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118576, "type": "TEXT_FIELD", "name": "createdViaId", "displayName": "Created Via Id", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118577, "type": "TEXT_FIELD", "name": "createdViaName", "displayName": "Created Via Name", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118578, "type": "TEXT_FIELD", "name": "createdViaType", "displayName": "Created Via Type", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118579, "type": "TEXT_FIELD", "name": "updatedViaId", "displayName": "Updated Via Id", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118580, "type": "TEXT_FIELD", "name": "updatedViaName", "displayName": "Updated Via Name", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118581, "type": "TEXT_FIELD", "name": "updatedViaType", "displayName": "Updated Via Type", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 118582, "type": "TEXT_FIELD", "name": "subSource", "displayName": "Sub Source", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118583, "type": "TEXT_FIELD", "name": "utmSource", "displayName": "UTM Source", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118584, "type": "TEXT_FIELD", "name": "utmCampaign", "displayName": "UTM Campaign", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118585, "type": "TEXT_FIELD", "name": "utmMedium", "displayName": "UTM Medium", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118586, "type": "TEXT_FIELD", "name": "utmContent", "displayName": "UTM Content", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 118587, "type": "TEXT_FIELD", "name": "utmTerm", "displayName": "UTM Term", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 138520, "type": "LOOK_UP", "name": "importedBy", "displayName": "Imported By", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "internal": true, "width": null, "systemRequired": false, "picklist": null}, {"id": 140762, "type": "TEXT_FIELD", "name": "cfDummyField", "displayName": "Dummy <PERSON>", "description": "This is Dummy <PERSON> Field", "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": 3, "max": 255, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 140763, "type": "TEXT_FIELD", "name": "cfDummyTextField", "displayName": "Dummy <PERSON>", "description": "Dummy <PERSON>", "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": 3, "max": 255, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 140764, "type": "PARAGRAPH_TEXT", "name": "cfDummyParagraphText", "displayName": "Dummy Paragraph Text", "description": "Dummy Paragraph Text", "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": 3, "max": 2550, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 140765, "type": "NUMBER", "name": "cfDummyNumberField", "displayName": "Dummy Number <PERSON>", "description": "Dummy Number <PERSON>", "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 140766, "type": "PICK_LIST", "name": "cfDummyPicklistField", "displayName": "Dummy <PERSON>list Field", "description": "Dummy <PERSON>list Field", "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": {"id": 4438, "name": "DUMMY_PICKLIST_FIELD", "picklistValues": [{"id": 13759, "name": "VALUE_2", "displayName": "Value 2", "disabled": false}, {"id": 13760, "name": "VALUE_3", "displayName": "Value 3", "disabled": false}, {"id": 13761, "name": "VALUE_4", "displayName": "Value 4", "disabled": false}, {"id": 13758, "name": "VALUE_1", "displayName": "Value 1", "disabled": false}]}}, {"id": 140767, "type": "MULTI_PICKLIST", "name": "cfDummyMultiPicklistField", "displayName": "Dummy Multi Picklist Field", "description": "Dummy Multi Picklist Field", "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 140768, "type": "CHECKBOX", "name": "cfDummyCheckboxField", "displayName": "Dummy Checkbox Field", "description": "Dummy Checkbox Field", "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 140769, "type": "DATE_PICKER", "name": "cfDummyDateField", "displayName": "Dummy <PERSON>", "description": "Dummy <PERSON>", "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 140770, "type": "DATETIME_PICKER", "name": "cfDummyDateTimePickerField", "displayName": "Dummy Date Time Picker Field", "description": "Dummy Date Time Picker Field", "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 140771, "type": "URL", "name": "cfDummyUrlField", "displayName": "Dummy <PERSON>", "description": "Dummy <PERSON>", "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 140772, "type": "MULTI_PICKLIST", "name": "cfDummyMultiPickList2", "displayName": "Dummy Multi Pick List 2", "description": "Test ", "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}, {"id": 152621, "type": "NUMBER", "name": "id", "displayName": "ID", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}]