{"variable_ownerId": "<PERSON><PERSON>", "resourceId": "5", "variable_companyZipcode": "Joseph <PERSON>", "variable_createdBy": "<PERSON><PERSON>", "variable_updatedViaId": "", "variable_companyName": "Murray and Herrera Trading", "variable_convertedAt": "", "variable_salutation": "Mr", "variable_companyAddress": "Fulton Delaney Inc", "variable_country": "IN", "variable_updatedBy": "<PERSON><PERSON>", "variable_requirementName": "Virginia Wise", "variable_companyBusinessType": "competitor", "variable_subSource": "Cillum molestiae tem", "variable_id": "489948", "variable_requirementBudget": "21.0", "variable_pipeline": "Default Lead Pipeline", "variable_requirementCurrency": "INR", "variable_twitter": "https://app-qa.sling-dev.com/sales/leads/list", "variable_companyEmployees": ["10-19"], "variable_lastName": "<PERSON>", "variable_campaign": "Organic", "variable_dnd": "", "variable_utmSource": "Aliquip incididunt d", "variable_timezone": "US/Alaska", "variable_companyAnnualRevenue": "20.0", "variable_emails": ["<EMAIL>", "<EMAIL>"], "variable_utmCampaign": "Sed eos voluptas di", "variable_updatedViaName": "", "variable_products": ["deal"], "variable_companyCountry": "AX", "variable_companyCity": "Walton Mcleod Traders", "variable_createdViaType": "Web", "variable_companyWebsite": "https://app-qa.sling-dev.com/sales/leads/list", "variable_updatedAt": "<PERSON><PERSON> <PERSON> 11 15:23:08 UTC 2024", "variable_companyIndustry": "ACCOUNTING", "variable_pipelineStageReason": "", "variable_actualClosureDate": "", "variable_pipelineStage": "Open", "variable_designation": "Qui possimus placea", "variable_firstName": "<PERSON>", "variable_state": "Maharashtra", "variable_facebook": "https://app-qa.sling-dev.com/sales/leads/list", "variable_score": "0.0", "variable_zipcode": "35969", "variable_importedBy": "", "variable_updatedViaType": "", "variable_createdAt": "<PERSON><PERSON> <PERSON> 11 15:23:08 UTC 2024", "variable_source": "Facebook", "variable_utmMedium": "<PERSON><PERSON><PERSON> alias i", "variable_expectedClosureOn": "", "variable_department": "<PERSON> laborum quis", "entityId": "489948", "variable_createdViaId": "8874", "variable_utmTerm": "<PERSON><PERSON><PERSON> praesentiu", "variable_convertedBy": "", "variable_address": "71 Fabien Extension", "variable_linkedIn": "https://app-qa.sling-dev.com/sales/leads/list", "variable_companyPhones": [{"type": "MOBILE", "code": "IN", "value": "**********", "dialCode": "+91", "primary": false}, {"type": "MOBILE", "code": "IN", "value": "6666666666", "dialCode": "+91", "primary": true}], "variable_companyState": "David and Whitfield LLC", "variable_createdViaName": "User", "variable_city": "Qui omnis non atque", "variable_utmContent": "Sit delectus expli", "variable_phoneNumbers": [{"type": "MOBILE", "code": "IN", "value": "7777777777", "dialCode": "+91", "primary": true}, {"type": "MOBILE", "code": "IN", "value": "9999999999", "dialCode": "+91", "primary": false}]}