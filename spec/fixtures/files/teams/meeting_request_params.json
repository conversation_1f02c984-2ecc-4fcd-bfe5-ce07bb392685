{"variable_title": "Meeting Workflow Test", "variable_providerLink": "", "variable_checkedOutLongitude": "", "resourceId": "21", "variable_checkedInLatitude": "", "variable_createdBy": "<PERSON><PERSON>", "variable_cancelledBy": "", "variable_conductedBy": "", "variable_description": "<div>Test Note</div>", "variable_status": "scheduled", "variable_updatedBy": "<PERSON><PERSON>", "variable_medium": "OFFLINE", "variable_id": "8673", "variable_location": "Pune", "variable_importedBy": "", "variable_createdAt": "Wed <PERSON> 24 11:44:56 UTC 2024", "variable_cancelledAt": "", "variable_timezone": "Asia/Calcutta", "variable_owner": "<PERSON><PERSON>", "variable_allDay": "false", "entityId": "8673", "variable_checkedOutLatitude": "", "variable_organizer": {"id": 8874, "entity": "USER", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "variable_checkedInAt": "", "variable_conductedAt": "", "variable_participants": [{"id": 489948, "entity": "LEAD", "name": "<PERSON>", "email": "<EMAIL>", "rsvpResponse": null, "rsvpMessage": null}, {"id": 489946, "entity": "LEAD", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "rsvpResponse": null, "rsvpMessage": null}, {"id": 8874, "entity": "USER", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "rsvpResponse": null, "rsvpMessage": null}], "variable_updatedAt": "Wed <PERSON> 24 11:44:56 UTC 2024", "variable_relatedTo": [{"id": 489948, "entity": "LEAD", "name": "<PERSON>", "email": "<EMAIL>"}, {"id": 489946, "entity": "LEAD", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "variable_to": "Wed Jan 24 12:00:00 UTC 2024", "variable_checkedOutAt": "", "variable_checkedInLongitude": "", "variable_from": "Wed <PERSON> 24 11:45:00 UTC 2024"}