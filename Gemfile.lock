GIT
  remote: https://github.com/galetahub/ckeditor.git
  revision: 8200ccdbf789669d154b800aab329db8f46aabdc
  specs:
    ckeditor (5.1.1)
      orm_adapter (~> 0.5.0)

GIT
  remote: https://github.com/kylastech/kylas_engine.git
  revision: 86b287a53311548c1170653c8747c4fecccaed00
  tag: v3.1.1
  specs:
    kylas_engine (3.0.0)
      devise
      pg
      rails (>= 7.0.2.3)
      sassc-rails
      sprockets-rails

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.4)
      public_suffix (>= 2.0.2, < 6.0)
    bcrypt (3.1.20)
    bindex (0.8.1)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    builder (3.2.4)
    coderay (1.1.3)
    concurrent-ruby (1.2.2)
    connection_pool (2.4.1)
    crack (0.4.5)
      rexml
    crass (1.0.6)
    date (3.3.3)
    debug (1.7.2)
      irb (>= 1.5.0)
      reline (>= 0.3.1)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.5.0)
    erubi (1.12.0)
    factory_bot (6.2.1)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.2.0)
      factory_bot (~> 6.2.0)
      railties (>= 5.0.0)
    ffi (1.15.5)
    globalid (1.1.0)
      activesupport (>= 5.0)
    hashdiff (1.0.1)
    honeybadger (5.2.1)
    i18n (1.12.0)
      concurrent-ruby (~> 1.0)
    io-console (0.6.0)
    io-wait (0.2.1)
    irb (1.6.4)
      reline (>= 0.3.0)
    json (2.6.3)
    jwt (1.5.6)
    logdna (1.5.0)
      concurrent-ruby (~> 1.0)
      json (~> 2.0)
      require_all (~> 1.4)
    lograge (0.12.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.20.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    method_source (1.0.0)
    mina (1.2.4)
      open4 (~> 1.3.4)
      rake
    mini_mime (1.1.2)
    minitest (5.18.0)
    msgpack (1.7.0)
    net-imap (0.3.4)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.1)
      timeout
    net-smtp (0.3.3)
      net-protocol
    nio4r (2.5.9)
    nokogiri (1.14.3-x86_64-linux)
      racc (~> 1.4)
    open4 (1.3.4)
    orm_adapter (0.5.0)
    pagy (5.10.1)
      activesupport
    pg (1.4.6)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    public_suffix (5.0.1)
    puma (5.6.5)
      nio4r (~> 2.0)
    racc (1.6.2)
    rack (2.2.6.4)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.5.0)
      loofah (~> 2.19, >= 2.19.1)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rake (13.0.6)
    redis (5.0.6)
      redis-client (>= 0.9.0)
    redis-client (0.14.1)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    reline (0.3.3)
      io-console (~> 0.5)
    request_store (1.5.1)
      rack (>= 1.4)
    require_all (1.5.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.2.5)
    rspec-core (3.12.2)
      rspec-support (~> 3.12.0)
    rspec-expectations (3.12.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-mocks (3.12.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-rails (6.0.1)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.11)
      rspec-expectations (~> 3.11)
      rspec-mocks (~> 3.11)
      rspec-support (~> 3.11)
    rspec-support (3.12.0)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    sidekiq (6.5.5)
      connection_pool (>= 2.2.2)
      rack (~> 2.0)
      redis (>= 4.5.0)
    sprockets (4.2.0)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    strscan (3.0.1)
    thor (1.2.1)
    tilt (2.1.0)
    timeout (0.3.2)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.0)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.18.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.6.7)

PLATFORMS
  x86_64-linux

DEPENDENCIES
  bootsnap
  ckeditor!
  debug
  factory_bot_rails (~> 6.2)
  honeybadger (~> 5.2)
  io-wait (= 0.2.1)
  jwt (~> 1.5, >= 1.5.4)
  kylas_engine!
  logdna (~> 1.5)
  lograge (~> 0.12.0)
  mina
  pagy (~> 5.9, >= 5.9.1)
  pg (~> 1.1)
  pry
  puma (~> 5.0)
  rails (~> 7.0.4, >= *******)
  redis-namespace
  rspec-rails (~> 6.0)
  sassc-rails
  sidekiq (~> 6)
  sprockets-rails
  strscan (= 3.0.1)
  tzinfo-data
  web-console
  webmock

RUBY VERSION
   ruby 3.1.0p0

BUNDLED WITH
   2.4.10
