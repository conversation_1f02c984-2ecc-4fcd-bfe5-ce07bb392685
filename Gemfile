source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby "3.1.0"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 7.0.4", ">= *******"

# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
gem "sprockets-rails"

# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"

# Use the Puma web server [https://github.com/puma/puma]
gem "puma", "~> 5.0"

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ mingw mswin x64_mingw jruby ]

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Use Sass to process CSS
gem "sassc-rails"

# Integrate kylas engine
gem 'kylas_engine', git: 'https://github.com/kylastech/kylas_engine.git', tag: 'v3.1.1'

# Add honeybadger for error monitoring
gem "honeybadger", "~> 5.2"

# integrate lograge to bring sanity to Rails' noisy and unusable, unparsable and, in the context of running multiple processes and servers, unreadable default logging output.
gem "lograge", "~> 0.12.0"

# integrate logdna for log management
gem "logdna", "~> 1.5"

# to decrypt the access token from microsoft
gem 'jwt', '~> 1.5', '>= 1.5.4'

# integrate sidekiq to run process in background
gem 'sidekiq', '~> 6'
gem 'redis-namespace'

# pagy for pagination
gem 'pagy', '~> 5.9', '>= 5.9.1'

# for rich text support
gem 'ckeditor', github: 'galetahub/ckeditor'

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri mingw x64_mingw ]

  # Debugger
  gem 'pry'

  # RSpecs
  gem "rspec-rails", "~> 6.0"

  # factory bot
  gem "factory_bot_rails", "~> 6.2"

  gem "webmock"
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console"

  # Add speed badges [https://github.com/MiniProfiler/rack-mini-profiler]
  # gem "rack-mini-profiler"

  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
end

gem 'io-wait', '0.2.1'
gem 'mina'
gem 'strscan', '3.0.1'
