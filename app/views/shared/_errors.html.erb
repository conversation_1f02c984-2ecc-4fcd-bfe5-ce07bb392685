<% if resource&.errors&.any? %>
  <% model_name ||= nil %>
  <div class="alert alert-danger alert-dismissible fade show mt-2" id='error_explanation'>
    <h6>
      <%= I18n.t('errors.messages.not_saved',
          count: resource.errors.count,
          resource: (model_name.presence || resource.class.model_name.human).downcase)
      %>
    </h6>
    <ul>
      <% resource.errors.full_messages.each do |msg| %>
        <li><%= msg.html_safe %></li>
      <% end %>
    </ul>
    <span class="btn-close" data-bs-dismiss="alert" aria-label="Close"></span>
  </div>
<% end %>
