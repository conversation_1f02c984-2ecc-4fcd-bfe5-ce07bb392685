<header>
  <nav class='navbar navbar-expand bg-white pb-1 pt-1'>
    <div class='header-top'>
      <nav class='navbar navbar-expand-lg navbar-light pb-0 pl-0'>
        <ul class='navbar-nav mr-auto'>
          <% tenant = current_tenant %>

          <% if tenant.present? && tenant.kylas_api_key.present? %>
            <li class='nav-item <%= active_class('notification_logs') %>'>
              <div class='left-navbar__side-nav__item-section'>
                <%= link_to t('notification_logs.title'), main_app.notification_logs_path, class: 'nav-link pl-0' %>
              </div>
            </li>

            <li class='nav-item <%= active_class('notification_templates') %>'>
              <div class='left-navbar__side-nav__item-section'>
                <%= link_to t('notification_templates.title'), main_app.notification_templates_path, class: 'nav-link pl-0' %>
              </div>
            </li>

            <li class='nav-item <%= active_class('manage_users') %>'>
              <div class='left-navbar__side-nav__item-section'>
                <%= link_to t('manage_users.title'), main_app.manage_users_path, class: 'nav-link pl-0' %>
              </div>
            </li>

            <li class='nav-item <%= active_class('connected_accounts').presence || active_class('agent_mappings') %>'>
              <div class='left-navbar__side-nav__item-section'>
                <%= link_to t('connected_accounts.title'), main_app.connected_accounts_path, class: 'nav-link pl-0' %>
              </div>
            </li>
          <% end %>

          <% if tenant.present? %>
            <li class='nav-item <%= active_class('tenants') %>'>
              <div class='left-navbar__side-nav__item-section'>
                <%= link_to t('tenants.kylas_api_key_navbar_placeholder'), kylas_engine.edit_tenant_path(tenant),
                    class: 'nav-link pl-0' %>
              </div>
            </li>
          <% end %>

          <li class='nav-item <%= active_class('dashboards') %>'>
            <div class='left-navbar__side-nav__item-section'>
              <%= link_to t('help'), kylas_engine.dashboard_help_path, class: 'nav-link pl-0' %>
            </div>
          </li>

        </ul>
      </nav>
      <div id="profile" class='header-top-right'>
        <div class='dropdown dropdown-user ml-2'>
          <button type="button" class="btn p-0 dropdown-toggle" data-bs-toggle="dropdown"
              data-bs-display="static" aria-expanded="false">
            <%= create_avatar_name %>
          </button>
          <ul class="dropdown-menu dropdown-menu-end dropdown-menu-lg-start">
            <%= link_to t('logout'), kylas_engine.destroy_user_session_path, { method: :delete,
                class: 'dropdown-item cursor-pointer' } %>
          </ul>
        </div>
      </div>
    </div>
  </nav>
</header>

<main class='main-content min-height-0 position-relative main-parent-wrapper'>
  <div class='dashboard-layout'>
    <div class='dashboard-layout__bucket'>
      <%= yield %>
    </div>
  </div>
</main>

<script>
  $(function () {
    if (window.top != window.self) {
      $('#profile').hide();
    }
  });
</script>
