<%= render 'devise/shared/flash' %>
<%= form_for(@tenant, url: kylas_engine.tenant_path(@tenant), html: { class: 'form-horizontal pb-6' }) do |f| %>
  <div class='form-group'>
    <%= f.label :webhook_api_key, t('tenants.api_key', app_name: APP_NAME), input_html: { class: 'form-label' }, class: 'app-name', id: 'api-key' %>
    <span toggle="#api-field" class="fa fa-fw fa-eye fa-lg toggle-webhook-api-key px-2 cursor-pointer"></span>
    <div class='h5 app-webhook-api-key'>
      <span class='vendor-link'><%= @tenant.webhook_api_key ? @tenant.webhook_api_key : I18n.t('tenants.generate_webhook_api_key', app_name: PRODUCT_NAME) %></span>
      <i class="fa-regular fa-copy copy-link ml-2 px-2 text-primary cursor-pointer"></i>
    </div>
    <div>
      <label class="secondary-label"><%= t('tenants.api_key_help', app_name: PRODUCT_NAME) %></label>
    </div>
  </div>
  <hr/>
  <div class='form-group mt-3'>
    <%= f.label :kylas_api_key, t('tenants.api_key', app_name: PRODUCT_NAME), class: 'form-label label-bold' %>
    <div class="input-group mb-3">
      <%= f.password_field :kylas_api_key, id:"password-field", value: @tenant.kylas_api_key, autocomplete: 'off', placeholder: t('tenants.api_key', app_name: PRODUCT_NAME), class: 'form-control border-color_rm', style: 'border-right: none;' %>
      <div class="input-group-append">
        <span toggle="#password-field" class="input-group-text toggle-product-api-key fa fa-fw fa-eye fa-lg pe-4 cursor-pointer" style='background: white;'>
          <%= button_tag(class: "btn", style: 'border-left: none;')%>
        </span>
      </div>
    </div>
  </div>

  <% if current_user.is_tenant? %>
  <div class="fixed-bottom border">
    <div class='form-group mt-2 d-flex justify-content-end'>
      <%= f.submit t('save'), id: 'submitBtn', class: 'btn btn-primary btn-sm px-4', data: { disable_with: t('saving')}, style: 'margin-right: 1%;' %>
    </div>
  </div>
  <% end %>
<% end %>

<script>
  $(document).ready(function() {
    $('.copy-link').click(function() {
      let api_key = $(this).closest('div').find('.vendor-link').html();
      navigator.clipboard.writeText(api_key).then(function() {
        alert("<%= t('tenants.copy_value', element: 'API Key') %>");
      }, function(err) {
        console.error('Async: Could not copy text: ', err);
      });
    });

    $(".toggle-product-api-key").click(function() {
      $(this).toggleClass("fa-eye fa-eye-slash");
      var input = $($(this).attr("toggle"));
      if (input.attr("type") == "password") {
        input.attr("type", "text");
      } else {
        input.attr("type", "password");
      }
    });

    $(".app-webhook-api-key").hide();

    $(".toggle-webhook-api-key").click(function() {
      $(this).toggleClass("fa-eye-slash fa-eye");
      if ($('.app-webhook-api-key').is(":hidden")) {
        $('.app-webhook-api-key').show();
      } else {
        $('.app-webhook-api-key').hide();
      }
    });
  });
</script>
