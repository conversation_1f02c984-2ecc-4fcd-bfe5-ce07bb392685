<%= render "devise/shared/flash" %>
<%= render 'shared/errors', resource: @agent_mapping %>

<%= form_for @agent_mapping, url: url, class: "form-horizontal mb-5" do |form| %>
  <%= form.hidden_field :name, required: true %>
  <%= form.hidden_field :identifier_type %>

  <div class="mb-3">
    <%= form.label :user_id, t("agent_mapping.select_user", type: PRODUCT_NAME), class: "required mb-2" %>
    <%= form.select :user_id, @kylas_users, { include_blank: t("agent_mapping.select_user", type: PRODUCT_NAME) }, { autocomplete: "off", class: "form-control form-select w-50", required: true } %>
  </div>
  <div class="mb-3">
    <%= form.label :identifier, t("agent_mapping.select_user", type: @agent_mapping.identifier_type.titleize), class: "required label-bold w-50 mb-2" %>
    <%= form.select :identifier,
      options_for_select(@users),
      { prompt: true },
      {
        class: "form-select form-control form-select w-50",
        required: true,
        onchange: 'setUserName(this)',
        placeholder: t("agent_mapping.select_user", type: @agent_mapping.identifier_type.titleize),
      }
    %>
  </div>
  <div class="fixed-bottom bottom-bar border bg-white justify-content-end">
    <div>
      <%= link_to t("cancel"), connected_account_agent_mappings_path(page: params[:page]), class: "btn btn-outline-primary btn-sm mr-3" %>
      <%= form.submit t("save"), class: "btn btn-primary btn-sm px-4", data: { disable_with: t("saving") } %>
    </div>
  </div>
<% end %>

<script>
  const teamsUserOptions = prepareOptions(<%= raw(@users.to_json) %>);

  $('#agent_mapping_identifier').select2({
    ajax: {
      url: '<%= autocomplete_connected_account_agent_mappings_path(@connected_account.id) %>',
      type: "get",
      dataType: 'json',
      delay: 250,
      data: (params) => ({ query: params.term }),
      processResults: function (response, params) {
        if(response['success'] == false) return { results : teamsUserOptions }

        return { results: prepareOptions(response['users']) };
      },
      cache: true
    }
  });

  function prepareOptions(userReponse){
    return userReponse.map((user)=>({ id: user[1], text: user[0] }));
  }

  function setUserName(event) {
    $("#agent_mapping_name").val($('option:selected', event).text());
  }
</script>
