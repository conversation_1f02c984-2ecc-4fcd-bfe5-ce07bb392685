<div class="main-container">
  <%= render 'devise/shared/flash' %>

  <div class="row justify-content-between">
    <div class="row w-100">
      <h4 class="col-6"><%= t('agent_mapping.title') %></h4>
      <% if @mappings.present? %>
        <div class="form-group text-end mt-1 col-6">
          <%= link_to new_connected_account_agent_mapping_path, class: "btn btn-outline-primary btn-sm mx-2" do %>
            <i class="fa-solid fa-plus"></i>
          <% end %>

          <% if @mapping_in_progress %>
            <button class="btn btn-sm btn-primary" type="button" disabled>
              <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
              <%= t('agent_mapping.syncing') %>
            </button>
          <% else %>
            <%= link_to t('agent_mapping.sync'), sync_connected_account_agent_mappings_path, method: :post, class: "btn btn-primary btn-sm", title: t('agent_mapping.sync_help_text') %>
          <% end %>
        </div>
      <% else %>
        <div class="form-group text-end mt-1 col-6">
          <%= link_to t('agent_mapping.sync'), sync_connected_account_agent_mappings_path, method: :post, class: "btn btn-primary btn-sm", title: t('agent_mapping.sync_help_text') %>
        </div>
      <% end %>
    </div>
  </div>

  <hr class="mt-0" />

  <% if @mappings.present? %>
    <%= render 'agent_mappings/table', mappings: @mappings %>
  <% else %>
    <div
      class="d-flex justify-content-center align-items-center flex-column"
      style="height: 200px"
    >
      <h4><%= t('agent_mapping.agent_not_found') %></h4>
      <%= link_to t('agent_mapping.add_agent'), new_connected_account_agent_mapping_path, class: "btn btn-primary" %>
    </div>
  <% end %>
</div>
