<%= render 'agent_mappings/delete_modal' %>

<div class="table-responsive mb-5">
  <table class="table table-bordered entity-table">
    <thead>
      <tr>
        <th style='width: 30%'><%= t('agent_mapping.name') %></th>
        <th style='width: 50%'><%= t('agent_mapping.id') %></th>
        <th style='width: 20%'><%= t('agent_mapping.actions') %></th>
      </tr>
    </thead>
    <tbody>
      <% @mappings.each do |agent_mapping| %>
        <tr class="align-middle">
          <td><%= agent_mapping.name %></td>
          <td>
            <div class="mb-1"><span class="agent-mappings"><%= t('agent_mapping.type_name', type: PRODUCT_NAME) %></span>: <%= agent_mapping.user.name %></div>
            <div class="mb-1"><span class="agent-mappings"><%= t('agent_mapping.type_id', type: agent_mapping.identifier_type.titleize) %></span>: <%= agent_mapping.identifier %></div>
          </td>
          <td>
            <%= link_to edit_connected_account_agent_mapping_path(@connected_account.id, agent_mapping.id, page: params[:page]), class: "btn btn-sm btn-outline-primary mx-1" do  %>
              <i class="fa-solid fa-pen"></i>
            <% end %>
            <%= link_to 'javascript:;', onclick: "showModal(#{agent_mapping.id})", class: "btn btn-sm btn-outline-danger mx-1" do  %>
              <i class="fa-solid fa-trash"></i>
            <% end %>
          </td>
        <tr>
      <% end %>
    </tbody>
  </table>
</div>

<div class="fixed-bottom border bg-white bottom-bar">
  <%== pagy_info(@pagy) %>
  <div class="d-flex flex-row">
    <%== pagy_bootstrap_nav(@pagy) %>
  </div>
</div>
