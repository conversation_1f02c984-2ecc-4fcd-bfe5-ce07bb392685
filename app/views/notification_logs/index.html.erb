<div class="main-container">
  <div class="row justify-content-between">
    <h4><%= t('notification_logs.title') %></h4>
  </div>

  <hr class="mt-0" />
  <%= render 'devise/shared/flash' %>
  <div>
    <% if @notification_logs.present? %>
      <%= render 'notification_logs/modal_show_message' %>

      <div class="table-responsive mb-5">
        <table class="table table-bordered entity-table">
          <thead>
            <tr>
              <th style='width: 10%'><%= t('notification_logs.entity_details') %></th>
              <th style='width: 25%'><%= t('connected_accounts.template_name') %></th>
              <th style='width: 15%'><%= t('notification_logs.recipient_name') %></th>
              <th style='width: 25%'><%= t('notification_logs.status') %></th>
              <th style='width: 15%'><%= t('notification_logs.notified_at') %></th>
              <th style='width: 10%'><%= t('notification_logs.actions') %></th>
            </tr>
          </thead>
          <tbody>
            <% @notification_logs.each do |log| %>
              <tr>
                <td class="entity-details">
                  <span class="entity-name">
                    <%= log.entity_type.singularize.titleize %>:
                    <% if log.template_name.present? %>
                      <%=
                        link_to log.entity_id,
                        log.entity_url(log.entity_id),
                        { target: '_blank' }
                      %>
                    <% end %>
                  </span>
                </td>

                <td><%= log.template_name %></td>

                <td><%= log.recipient_name %></td>

                <td class="log-status">
                  <%= log.status.capitalize %>
                  <% if log.status.upcase == FAILED %>
                    <span class="error-message"> <%= log.error_message %> </span>
                  <% end %>
                </td>

                <td class="notified-at"> <%= log.created_at %> </td>

                <td>
                  <% if log.notification_content.present? %>
                    <%=
                      link_to t('view_message'), '#',
                      {
                        'onclick': 'showMessage(this)',
                        'data-content': CGI.unescapeHTML(log.notification_content),
                        title: t('notification')
                      }
                    %>
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    <% else %>
      <div
        class="d-flex justify-content-center align-items-center flex-column"
        style="height: 200px"
      >
        <h4><%= t('notification_logs.not_found') %></h4>
      </div>
    <% end %>

    <div class="fixed-bottom border bg-white bottom-bar">
      <%== pagy_info(@pagy) %>
      <div class="d-flex flex-row">
        <%== pagy_bootstrap_nav(@pagy) %>
      </div>
    </div>
  </div>
</div>

<script>
  $('.notified-at').each(function() {
    $(this).text(`${moment($(this).text()).local().format('MMM DD, YYYY [at] h:mm a')}`)
  });

  function showMessage(element){
    const content = $(element).attr('data-content');
    const modalContainer = $('#notification-log-message');
    modalContainer.html(content);
    $('#message-modal').modal('show');
  }
</script>
