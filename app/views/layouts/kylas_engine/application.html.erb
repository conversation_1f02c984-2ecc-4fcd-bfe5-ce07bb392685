<!DOCTYPE html>
<html>
  <head>
    <title><%= t("app_name") %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "application" %>
    <%= javascript_include_tag "application" %>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
  </head>

  <body>
    <% if user_signed_in? %>
      <div class="d-flex flex-column h-100" id="app">
        <div class="d-flex flex-column h-100">
          <%= render 'shared/kylas_engine/navbar' %>
        </div>
      </div>
    <% else %>
      <%= yield %>
    <% end %>

    <script>
      $(document).on('select2:open', () => { $('.select2-search__field')[0].focus(); });
    </script>
  </body>
</html>
