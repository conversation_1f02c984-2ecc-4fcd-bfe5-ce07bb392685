<%= render "devise/shared/flash" %>
<%= form_for @user, url: manage_user_path, class: "form-horizontal mb-5" do |form| %>
  <div class="mb-3">
    <%= form.label :kylas_user_id, t("manage_users.app_user_name", product_name: PRODUCT_NAME), class: "required mb-2" %>
    <%= form.select :kylas_user_id,
      options_for_select(@kylas_users, selected: @user.id),
      { include_blank: t("manage_users.select_user") },
      { autocomplete: "off", class: "form-control form-select w-50", required: true, disabled: true }
    %>
  </div>
  <div class="mb-3">
    <%= form.label :email, t("manage_users.user_email"), class: "required label-bold" %>
    <%= form.email_field :email, autocomplete: "off", placeholder: t("users.select_user", product_name: PRODUCT_NAME), class: "form-control fields w-50", required: true, value: @user.email, disabled: true %>
  </div>
  <div class="mb-3">
    <%= form.label :name, t("manage_users.user_name"), class: "required label-bold" %>
    <%= form.text_field :name, autocomplete: "off", placeholder: t("select_user_name"), class: "form-control fields w-50", required: true, autofocus: true %>
  </div>
  <div class="fixed-bottom bottom-bar border bg-white justify-content-end">
    <div>
      <%= link_to t("cancel"), manage_users_path, class: "btn btn-outline-primary btn-sm mr-3" %>
      <%= form.submit t("save"), class: "btn btn-primary btn-sm px-4", data: { disable_with: t("saving") } %>
    </div>
  </div>
<% end %>
