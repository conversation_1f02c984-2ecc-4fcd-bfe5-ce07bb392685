<div class="main-container">
  <%= render 'devise/shared/flash' %>

  <div class="row justify-content-between">
    <div class="row w-100">
      <h4 class="col-6"><%= t('manage_users.title') %></h4>
      <div class="form-group text-end mt-1 col-6">
        <% if @fetch_users_in_progress %>
          <button class="btn btn-sm btn-primary" type="button" disabled>
            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            <%= t('manage_users.fetching') %>
          </button>
        <% else %>
          <%= link_to t('manage_users.fetch_and_map_users'), sync_manage_users_path, method: :post, class: "btn btn-primary btn-sm" %>
        <% end %>
      </div>
    </div>
  </div>

  <hr class="mt-0" />

  <%= render 'manage_users/users_table', connections: @user_mapping %>
</div>
