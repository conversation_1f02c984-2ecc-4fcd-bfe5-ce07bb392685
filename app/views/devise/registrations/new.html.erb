<div class='authentication-layout'>
  <div class='kylas-background'></div>
  <div class='content-wrapper'>
    <div class='main-content'>
      <div class='authentication-form-wrapper'>
        <h1><%= t("sign_up") %></h1>
        <div class='mb-4'>
          <strong>
            <span><%= t("existing_account") %></span>
            <%= link_to 'Sign in here', new_session_path(resource_name), class: 'link-primary' %>
          </strong>
        </div>
        <%= render 'devise/shared/flash' %>
        <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: {id: 'signUpForm'}) do |f| %>
          <div class='form-group'>
            <%= f.label 'Name', input_html: { class: 'form-label' } %>
            <div class='text-field col-undefined'>
              <div class='form-group' id='lastName'>
                <div class='validate'>
                  <%= f.text_field :name, autocomplete: 'off', placeholder: 'Name', id: 'name', class: 'form-control' %>
                </div>
              </div>
            </div>
          </div>

          <% if resource.errors.any? && resource.errors.messages[:name].any? %>
            <span class='error'>
              <%= t("name") %>
              <%= resource.errors.messages[:name][0] %>
            </span>
          <% end %>

          <div class='form-group'>
            <%= f.label 'Email ID', input_html: { class: 'form-label' } %>
            <div class='text-field col-undefined'>
              <div class='form-group' id='email'>
                <div class='validate'>
                  <%= f.email_field :email, autocomplete: 'off', placeholder: 'Email', id: 'input_email', class: 'form-control' %>
                </div>
              </div>
            </div>
          </div>
          <% if resource.errors.any? && resource.errors.messages[:email].any? %>
            <span class='error'>
              <%= t("your_email") %>
              <%= resource.errors.messages[:email][0] %>
            </span>
          <% end %>

          <div class='form-group'>
            <%= f.label 'Password', input_html: { class: 'form-label' } %>
            <div class='text-field col-undefined'>
              <div class='input-group flex' id='password'>
                <div class='validate password-box'>
                  <%= f.password_field :password, autocomplete: 'current-password', placeholder: 'Password', id: 'input_password', class: 'form-control form-control--password' %>
                </div>
                <div class="input-group-append">
                  <span toggle=".form-control--password" class="input-group-text toggle-password fa fa-fw fa-eye-slash field-icon pe-4 cursor-pointer" style='border-left: none; line-height: 1.5rem'>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <% if resource.errors.any? && resource.errors.messages[:password].any? %>
            <span class='error'>
              <%= t("password") %>
              <%= resource.errors.messages[:password][0] %>
            </span>
          <% end %>

          <div class='form-group w-100 mb-0'>
            <%= f.submit 'Sign Up For Free', id: 'loginBtn', class: 'btn btn-md w-100 mb-3 mt-auto btn-primary' %>
          </div>
          <p class='f-13 m-0 acknowledgement text-justify'>
            <%= t("sign_up_text") %>
            <a class='link-primary' href='<%= TERMS_URL %>' target='_blank'> <%= t("terms") %></a>
            <%= t("term_acknowledge_text") %>
            <a class='link-primary' href='<%= PRIVACY_URL %>' target='_blank'> <%= t("privacy_policy").downcase %></a>.
          </p>
        <% end %>
      </div>
    </div>
  </div>
</div>
<script>
  $(document).ready(function(){
    $('#signUpForm').validate({
      rules: {
        'user[email]': {
          required: true,
          email: true
        },
        'user[name]': 'required',
        'user[password]': 'required'
      },

      messages: {
        'user[email]': 'Please enter valid email',
        'user[name]': 'Please enter name',
        'user[password]': 'Please enter password'
      }
    });

    $(".toggle-password").click(function() {
      $(this).toggleClass("fa-eye-slash fa-eye");
      var input = $($(this).attr("toggle"));
      if (input.attr("type") == "password") {
        input.attr("type", "text");
      } else {
        input.attr("type", "password");
      }
    });
  });
</script>
