<%= csrf_meta_tags %>
<div class='authentication-layout'>
  <div class='kylas-background'></div>

  <div class='content-wrapper'>
    <div class='main-content'>
      <div class='authentication-form-wrapper'>
        <%= image_tag 'kylas_engine/logo.png', class: 'logo', alt: 'logo' %>
        <h1><%= t("sign_in") %></h1>
        <%= image_tag 'kylas_engine/sign-in.svg', class: 'sign-in__placeholder', alt: 'sign in placeholder' %>
        <p class='sign-in__mobile-help-text'><%= t("sign_in_desktop") %></p>
        <div class='mb-4'>
          <strong class='signup_link'>
            <span><%= t("dont_have_account") %></span>
            <%= link_to 'Sign up here', new_registration_path(resource_name), class: 'link-primary' %>
          </strong>
        </div>
        <%= render 'devise/shared/flash' %>
        <%= form_for(resource, as: resource_name, url: session_path(resource_name), html: {id: 'signInForm'}) do |f| %>
          <div class='form-group'>
            <%= f.label 'Email ID', input_html: { class: 'form-label' } %>
            <div class='text-field col-undefined'>
              <div class='form-group' id='email'>
                <div class='validate'>
                  <%= f.email_field :email, autocomplete: 'off', placeholder: 'Email', id: 'input_email', class: 'form-control' %>
                </div>
              </div>
            </div>
          </div>
          <% if resource.errors.any? && resource.errors.messages[:email].any? %>
            <span class='error'>
              <%= t("your_email") %>
              <%= resource.errors.messages[:email][0] %>
            </span>
          <% end %>
          <div class='form-group'>
            <%= f.label 'Password', :input_html => { :class => 'form-label' } %>
            <div class='input-group flex' id='password'>
              <div class='validate password-box'>
                <%= f.password_field :password, autocomplete: 'current-password', placeholder: 'Password', id: 'input_password', class: 'form-control form-control--password' %>
              </div>
              <div class="input-group-append">
                <span toggle=".form-control--password" class="input-group-text toggle-password fa fa-fw fa-eye-slash field-icon pe-4 cursor-pointer" style='border-left: none; line-height: 1.5rem'>
                </span>
              </div>
            </div>
          </div>
          <% if resource.errors.any? && resource.errors.messages[:password].any? %>
            <span class='error'>
              <%= t("password") %>
              <%= resource.errors.messages[:password][0] %>
            </span>
          <% end %>
          <div class='loggedin-forgot-password-link-wrapper'>
            <div class='custom-control custom-checkbox custom-control-inline'>
              <input class='custom-control-input' id='customCheckInline1' name='rememberMe' type='checkbox'></input>
              <label class='custom-control-label' for='customCheckInline1'><%= t("keep_me_logged_in") %></label>
            </div>
            <%= link_to 'Forgot Password?', new_password_path(resource_name) %>
          </div>
          <div class='actions'></div>
          <%= f.submit 'Sign in', id: 'loginBtn', class: 'btn btn-md w-100 mb-3 mt-auto btn-primary' %>
        <% end %>
        <div class='bottom-links'>
          <ul class='support-links'>
            <li>
              <a href='<%= CONTACT_URL %>' target='_blank'><%= t("contact_us") %></a>
            </li>
            <li class='separator'></li>
            <li>
              <a href='<%= PRIVACY_URL %>' target='_blank'><%= t("privacy_policy") %></a>
            </li>
            <li class='separator'></li>
            <li>
              <a href='<%= SUPPORT_URL %>' target='_blank'><%= t("support") %></a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
  $(document).ready(function(){
    $('#signInForm').validate({
      rules: {
        'user[email]': 'required',
        'user[password]': 'required'
      },

      messages: {
        'user[email]': 'Please enter valid email',
        'user[password]': 'Please enter password'
      }
    });

    $(".toggle-password").click(function() {
      $(this).toggleClass("fa-eye-slash fa-eye");
      var input = $($(this).attr("toggle"));
      if (input.attr("type") == "password") {
        input.attr("type", "text");
      } else {
        input.attr("type", "password");
      }
    });
  })
</script>
