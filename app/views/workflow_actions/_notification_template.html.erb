<%= javascript_include_tag Ckeditor.cdn_url %>

<section class="template-main-body mb-4">
  <%= form_for @workflow_action, url: save_workflow_actions_url, class: "template-form", method: :post do |form| %>
    <%= form.hidden_field :entity_type, value: @entity_type %>
    <%= form.hidden_field :tenant_id, value: @tenant_id %>
    <%= form.hidden_field 'userId', value: @user_id %>
    <%= form.hidden_field 'identifier', value: @identifier %>
    <%= form.hidden_field 'resourceId', value: @resourceId %>

    <div class="form-group fw-bold mt-3">
      <%= form.label :notification_template_id, t('notification_templates.select_template'), class: 'mb-2 required' %>
      <%=
        form.select :notification_template_id,
        options_for_select(@notification_templates&.collect { |template| [template.title, template.id] } || [], @workflow_action&.notification_template_id),
        { prompt: true },
        {
          class: 'form-select',
          id: 'template-selector',
          onchange: 'showContentInEditor()'
        }
      %>
    </div>

    <div class="fw-bold my-4">
      <%= form.label 'content', t('notification_templates.preview'), class: 'mb-2' %>
      <%= form.cktext_area 'content', as: :ckeditor, required: true, class: 'form-control', id: 'body-field', value: CGI.unescapeHTML(@content.to_s), disabled: true %>
    </div>

    <div class="fixed-bottom border bg-white">
      <div class='form-group mt-3 d-flex justify-content-end'>
        <%= link_to t('cancel'), '#', class: 'btn btn-outline-primary btn-sm', onclick: 'closeMarketplaceIframe()' %>
        <%= form.submit t('save'), id: 'submitBtn', class: 'btn btn-primary btn-sm mx-3', data: { disable_with: t('saving') } %>
      </div>
    </div>
  <% end %>
</section>

<script>
  $.blockUI();

  const templates = <%=
    raw @notification_templates&.collect{ |template| [template.id, template.title, template.active, template.content] }
  %>

  window.addEventListener("load",async () => {
    $('#template-selector').select2();

    CKEDITOR.on('instanceReady', function(event) { $.unblockUI() });

    showContentOfSelectedTemplate(templateSelector.value);
  })

  const bodyField = CKEDITOR.instances['body-field'];

  const templateSelector = document.querySelector('#template-selector')

  function showContentInEditor() {
    if(!templateSelector.value) return;

    showContentOfSelectedTemplate(templateSelector.value)
    removeInActiveTemplate();
  }

  function removeInActiveTemplate() {
    let storedTemplateId = JSON.parse('<%= raw (@workflow_action&.notification_template_id || '').to_json %>');
    if (storedTemplateId != '') {
      selectedTemplate = templates.filter(template => template[0] == storedTemplateId)[0];
      if (selectedTemplate[2] == false) { $(`#whatsappTemplate option[value=${storedTemplateId}]`).remove(); }
    }
  }

  function showContentOfSelectedTemplate(templateId) {
    selectedTemplate = templates.filter(template => template[0] == templateId)[0];
    if(!selectedTemplate) return;

    $.ajax({
      url: '/notification-templates/fetch-content',
      method: 'POST',
      data: {
        entity_fields: '<%= raw @entity_fields.to_json %>',
        template_id: selectedTemplate[0]
      },
      success: (response) => {
        if(response && response['success']){ bodyField.setData(response['content'])  }
      },
    });

    if (selectedTemplate[2] == false) {
      $(`#template-selector option[value=${templateId}]`).text(`${selectedTemplate[1]} (Inactive)`);
    }
  }

  function closeMarketplaceIframe(){
    window.parent.postMessage(
      {
        payload: { closeMarketplaceIframe: true },
        location: JSON.parse('<%= sanitize WORKFLOW.to_json %>'),
        identifier: JSON.parse('<%= sanitize params[:identifier].to_json %>')
      },
      JSON.parse('<%= raw WEB_APP_URL.to_json %>')
    );
  }
</script>
