<% if @errors.present? %>
  <div class="alert alert-danger">
    <ul>
      <% @errors.each do |msg| %>
        <li>
          <%= msg %>
        </li>
      <% end %>
      <li><%= t('failure_and_contact_message', product_name: PRODUCT_NAME) %></li>
    </ul>
  </div>
<% else %>
  <h5 class='font-weight-bolder text-primary'><%= t('please_wait') %></h5>

  <script type='text/javascript'>
    $(document).ready(function (params) {
      $.blockUI();
      parent.postMessage(
        {
          payload: JSON.parse('<%= sanitize @workflow_payload.to_json %>'),
          location: JSON.parse('<%= sanitize WORKFLOW.to_json %>'),
          identifier: JSON.parse('<%= sanitize params[:workflow_action][:identifier].to_json %>')
        },
        JSON.parse('<%= raw WEB_APP_URL.to_json %>')
      )
      $.unblockUI()
    })
  </script>
<% end %>
