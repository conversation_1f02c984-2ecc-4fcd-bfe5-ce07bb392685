<div class="main-container">
  <%= render 'devise/shared/flash' %>
  <div class="row justify-content-between">
    <div class="col-2 w-100">
      <h4><%= t('connected_accounts.title') %></h4>
    </div>
  </div>
  <hr class="mt-0" />

  <div class="content-holder">
    <% if @connected_account %>
      <%= render 'connected_accounts/modal_connect' %>
      <%= render 'connected_accounts/modal_disconnect' %>
      <div class="table-responsive">
        <table class="table table-bordered mb-5">
          <thead>
            <tr>
              <th style='width: 25%'><%= t('connected_accounts.name') %></th>
              <th style='width: 30%'><%= t('connected_accounts.email') %></th>
              <th style='width: 15%'><%= t('connected_accounts.status') %></th>
              <th style='width: 30%'><%= t('connected_accounts.actions') %></th>
            </tr>
          </thead>
            <tbody>
              <tr>
                <td><%= @connected_account.name %></td>
                <td><%= @connected_account.email %></td>
                <td><%= @connected_account.status.capitalize %></td>
                <td>
                  <%= link_to t('agent_mapping.view_mappings'), connected_account_agent_mappings_path(@connected_account), class: "btn btn-sm btn-outline-primary mx-1" %>
                  <% if @connected_account.status == ACTIVE %>
                    <%= link_to t('reconnect'), "#{MICROSOFT_AUTH_URI}", class: "btn btn-sm btn-outline-primary mx-1", target: :_blank %>
                    <%= link_to t('connected_account.disconnect'), "#", class: "btn btn-sm btn-danger mx-1", 'data-bs-toggle': "modal", 'data-bs-target': "#disconnect-modal" %>
                  <% else %>
                    <%= link_to t('connected_accounts.connect'), "#{MICROSOFT_AUTH_URI}", class: "btn btn-sm btn-outline-primary mx-1", target: :_blank %>
                  <% end %>
                </td>
              </tr>
          </tbody>
        </table>
      </div>
    <% else %>
      <div
        class="d-flex justify-content-center align-items-center flex-column"
        style="height: 200px"
      >
        <h4><%= t('connected_accounts.no_account_connected') %></h4>
        <%= link_to t('connected_accounts.connect_account'), "#{MICROSOFT_AUTH_URI}", class: "btn btn-primary", target: :_blank %>
      </div>
    <% end %>
  </div>
</div>
