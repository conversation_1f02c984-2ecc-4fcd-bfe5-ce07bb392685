<%= render 'devise/shared/flash' %>

<div class="row justify-content-between">
  <div class="row w-100">
    <h4 class="col-6"><%= t('notification_templates.title') %></h4>
    <% if @notification_templates.present? %>
      <div class="form-group text-end mt-1 col-6">
        <%= link_to t('notification_templates.add_template'), new_notification_template_path, class: 'btn btn-primary btn-sm' %>
      </div>
    <% end %>
  </div>
</div>
<hr class="mt-0" />

<% if @notification_templates.present? %>
  <div class="table-responsive mb-5">
    <table class="table table-bordered entity-table">
      <thead>
        <tr>
          <th style='width: 25%'><%= t('notification_templates.notification_title') %></th>
          <th style='width: 10%'><%= t('notification_templates.entity_type') %></th>
          <th style='width: 20%'><%= t('notification_templates.notify_to') %></th>
          <th style='width: 10%'><%= t('notification_templates.active') %></th>
          <th style='width: 15%'><%= t('notification_templates.actions') %></th>
        </tr>
      </thead>
      <tbody>
        <% @notification_templates.each do |template| %>
          <tr class="align-middle">
            <td><%= template.title %></td>
            <td><%= template.entity_type.present? ? template.entity_type.titleize : '' %></td>
            <td>
              <% template.notifiers.each do |notifier| %>
                <span style="display: block;"><%= notifier.recipient_name %> (<%= notifier.recipient_type.titleize %>)</span>
              <% end %>
            </td>
            <td><%= template.active ? t('yes_label') : t('no_label') %></td>
            <td>
              <%= link_to edit_notification_template_path(template.id), class: 'btn btn-primary btn-sm' do %>
                <i class='fa-sharp fa-solid fa-pen' aria-hidden='true'></i>
              <% end %>

              <%= link_to t('notification_templates.clone'), clone_notification_template_path(template.id), class: 'btn btn-outline-primary btn-sm mx-2' %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>

    <% if @pagy.present? && @pagy.count.positive? %>
      <div class="fixed-bottom border bg-white bottom-bar">
        <%== pagy_info(@pagy) %>
        <div class="d-flex flex-row">
          <%== pagy_bootstrap_nav(@pagy) %>
        </div>
      </div>
    <% end %>
  </div>
<% else %>
  <div
    class="d-flex justify-content-center align-items-center flex-column"
    style="height: 200px"
  >
    <h4><%= t('notification_templates.not_found') %></h4>
    <%= link_to t('notification_templates.add_template'), new_notification_template_path, class: 'btn btn-primary' %>
  </div>
<% end %>

<script>
  $('.created-at').each(function() {
    $(this).text(`${moment($(this).text()).local().format('MMM DD, YYYY [at] h:mm a')}`)
  });
</script>
