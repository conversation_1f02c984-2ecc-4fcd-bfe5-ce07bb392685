<%= javascript_include_tag Ckeditor.cdn_url %>

<section class="template-main-body mb-5">
  <%= render 'devise/shared/flash' %>
  <%= render "error_alert" %>

  <%= form_for @notification_template, class: "template-form" do |form| %>
    <%= render 'shared/errors', resource: @notification_template %>
    <% is_edit_screen = action_name == 'edit' || (@notification_template.clone == true) %>
    <%= form.hidden_field 'entity_fields', value: @entity_fields.to_json %>

    <div class="row form-group fw-bold mt-3 col">
      <div class="col-6">
        <%= form.label :title, t('title'), class: 'required mb-2' %>
        <%= form.text_field :title, required: true, placeholder: t('notification_templates.add_title') , class: "form-control" %>
      </div>

      <div class="col-6">
        <%= form.label :entity_type, t('notification_templates.entity_type'), class: 'required mb-2' %>
        <%=
          form.select :entity_type,
          options_for_select(ALLOWED_ENTITIES.map { |entity| [entity.titleize, entity] }, @selected_entity),
          { prompt: true },
          {
            class: 'form-select',
            onchange: 'fetch_entity_variables(this)',
            required: true,
            disabled: is_edit_screen,
            id: 'entity-type-selector'
          }
        %>
      </div>
    </div>

    <div class="row fw-bold mt-3 align-items-end">
      <div class="col-6">
        <%= form.label :notifiers, t('notification_templates.notify_to'), class: 'required mb-2' %>
        <%= form.select :notifiers,
          grouped_options_for_select(@notify_options || [], @notifiers || []),
          { prompt: true },
          {
            class: 'form-select',
            id: "notify-to-selecor",
            required: true,
            disabled: true,
            multiple: "multiple"
          }
        %>
      </div>

      <div class="col-6">
        <%= form.label 'variable', t('notification_templates.insert_variable'), class: 'mb-2' %>

        <div class="d-flex">
          <%=
            select_tag 'variable',
            options_for_select(@variables || []),
            {
              class: 'form-select flex-grow-1',
              id: 'variables-field',
              readonly: is_edit_screen,
              disabled: @variables.blank?
            }
          %>

          <%= link_to t('notification_templates.insert'), "#", class: 'btn btn-primary disabled mx-2', id: 'insert-button' %>
        </div>
      </div>
    </div>

    <div class="row fw-bold mt-3 justify-content-between align-items-end">
      <div class="col-6">
        <%= form.label :heading, t('notification_templates.heading'), class: 'required mb-2' %>
        <%= form.text_field :heading, required: true, placeholder: t('notification_templates.add_heading'), class: 'form-control', id: 'heading-field', onfocus: 'updateFocusElement(this)', value: @heading %>
      </div>

      <div class="col-6">
        <%= form.label 'mentions', t('notification_templates.mentions'), class: 'mb-2' %>

        <div class="d-flex">
          <%=
            select_tag 'mentions',
            options_for_select([]),
            {
              class: 'form-select flex-grow-1',
              id: 'mentions-field',
              readonly: is_edit_screen
            }
          %>

          <%= link_to t('notification_templates.insert'), "#", class: 'btn btn-primary disabled mx-2', id: 'mention-insert-button' %>
        </div>
      </div>
    </div>

   <div class="form-group fw-bold mt-3 w-100">
      <%= form.label :message_type, t('notification_templates.message_type'), class: "required w-100 mb-2" %>

      <%= form.radio_button :message_type, 'normal', class: "d-none" %>
      <%= form.label :message_type, t('notification_templates.message_type'), for: "notification_template_message_type_normal", title: t('notification_templates.standard_message_helper'), class: "mx-2" do %>
        <div style="aspect-ratio: 3" class="btn btn-sm btn-outline-primary normal-btn message-type-btn">
          <i class="fa-regular fa-comment"></i>
        </div>
        <span style="font-weight: normal"><%= MESSAGE_PRIORITY_MAPPING[:normal] %></span>
      <% end %>

      <%= form.radio_button :message_type, 'high', class: "d-none" %>
      <%= form.label :message_type, t('notification_templates.message_type'), for: "notification_template_message_type_high", title: t('notification_templates.important_message_helper'), class: "mx-2" do %>
        <div style="aspect-ratio: 3" class="btn btn-sm btn-outline-danger high-btn message-type-btn">
          <i class="fa-solid fa-exclamation"></i>
        </div>
        <span style="font-weight: normal"><%= MESSAGE_PRIORITY_MAPPING[:high] %></span>
      <% end %>

      <%= form.radio_button :message_type, 'urgent', class: "d-none" %>
      <%= form.label :message_type, t('notification_templates.message_type'), for: "notification_template_message_type_urgent", title: t('notification_templates.urgent_message_helper'), class: "mx-2" do %>
        <div style="aspect-ratio: 3" class="btn btn-sm btn-outline-danger urgent-btn message-type-btn">
          <i class="fa-solid fa-bell"></i>
        </div>
        <span style="font-weight: normal"><%= MESSAGE_PRIORITY_MAPPING[:urgent] %></span>
      <% end %>
    </div>

    <div class="form-group fw-bold mt-3">
      <%= form.label :content, t('notification_templates.body'), class: 'required mb-2' %>
      <%= form.cktext_area :content, as: :ckeditor, required: true, class: 'form-control', id: 'body-field', value: CGI.unescapeHTML(@content.to_s) %>
    </div>

    <div class="form-group my-3">
      <div class="form-check form-switch">
        <%= form.check_box :active, class: 'form-check-input' %>
        <%= form.label :active, t('active'), class: 'form-check-label' %>
      </div>
    </div>

    <div class="fixed-bottom border bg-white mt-2">
      <div class='form-group mt-3 d-flex justify-content-end'>
        <%= link_to t('cancel'), notification_templates_path, class: 'btn btn-outline-primary btn-sm mx-3' %>
        <%= form.submit t('save'), id: 'submitBtn', class: 'btn btn-primary btn-sm px-4', data: { disable_with: t('saving') } %>
      </div>
    </div>
  <% end %>
</section>

<script>
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
  const tooltipList = tooltipTriggerList.map((tooltipTriggerEl) => ( new bootstrap.Tooltip(tooltipTriggerEl) ))
  let notifyToOptions = [];

  const HEADING = 'HEADING';
  const BODY = 'BODY';
  let currentFocusedElement = BODY;

  window.addEventListener("load",() => {
    const insertButton = document.querySelector("#insert-button");
    const mentionInsertButton = document.querySelector("#mention-insert-button");

    CKEDITOR.on('instanceReady', function(event) {
      event.editor.on('focus', () => { currentFocusedElement = BODY });
      $(insertButton).removeClass("disabled");
      $(mentionInsertButton).removeClass("disabled");
    });

    $('#variables-field').select2().on('select2:open', () => { $('.select2-search__field')[1].focus(); });
    $('#notify-to-selecor').select2();
    $('#entity-type-selector').select2();
    $('#mentions-field').select2({
      minimumInputLength: 3,
      ajax: {
        url: '<%= fetch_mention_users_notification_templates_path %>',
        type: "get",
        dataType: 'json',
        delay: 250,
        data: (params) => ({ query: params.term }),
        processResults: function (response, params) {
          if(response['success'] == false) return { results : [] }

          return { results: response['users'] };
        },
        cache: true
      }
    }).on('select2:open', () => { $('.select2-search__field')[1].focus(); });

    if(insertButton){
      insertButton.addEventListener("click",() => {
        const headingField = document.querySelector('#heading-field');
        const bodyField = CKEDITOR.instances['body-field'];
        const variableSelector = document.querySelector("#variables-field");
        if(variableSelector.value){
          switch(currentFocusedElement) {
            case HEADING:
              var currentFieldValue = headingField.value
              const startPosition = headingField.selectionStart;
              const endPosition   = headingField.selectionEnd;
              headingField.value = `${currentFieldValue.substring(0,startPosition)} ${variableSelector.value} ${currentFieldValue.substring(endPosition, currentFieldValue.length)}`;
              break
            case BODY:
              var currentFieldValue = bodyField.getData();
              bodyField.insertHtml(variableSelector.value.replace(/(if missing:)\s*<(.*?)>/g, '$1 &lt;$2&gt;'))
              break
          }
        }
      });
    }

    if(mentionInsertButton){
      mentionInsertButton.addEventListener("click", () => {
        const bodyField = CKEDITOR.instances['body-field'];
        const mentionSelector = document.querySelector("#mentions-field");
        if(!mentionSelector.value){ return; }

        bodyField.insertHtml(mentionSelector.value);
      });
    }

    $(".<%= @notification_template.message_type %>-btn").addClass("active");
    $('input[type="radio"][name="notification_template[message_type]"]').change(checkPriorityButtonElement);
    fetchAndSetNotifyOptions();
  });

  function checkPriorityButtonElement(){
    $(".message-type-btn").removeClass("active");
    switch(this.id){
      case "notification_template_message_type_normal":
        $(".normal-btn").addClass("active");
        break;
      case "notification_template_message_type_high":
        $(".high-btn").addClass("active");
        break;
      case "notification_template_message_type_urgent":
        $(".urgent-btn").addClass("active");
        break;
    }
  }

  function updateFocusElement(event){ currentFocusedElement = HEADING }

  function fetch_entity_variables(event){
    const entityType = event.value
    const variablesField = document.querySelector('#variables-field')

    if (entityType == ''){
      variablesField.disabled = true;
      $("#variables-field").empty();
      return
    }

    $.ajax({
      url: `/notification-templates/${event.value}/fetch-variables`,
      type: "GET",
      success: function (data) {
        if(data['success']){
          const entity_fields = data['entity_fields']
          const variables = JSON.parse(entity_fields)
          const options = variables.map(option => (
            new Option(`{{${option['displayName']}}}`, `{{${option['displayName']}}, {if missing: <free text>}}`)
          ));
          $('#variables-field').empty().append(options).trigger('change');
          $('#notification_template_entity_fields').val(entity_fields)
          variablesField.disabled = false;
        }
        else{ variablesField.disabled = true; }
      },
      error: function (error) { variablesField.disabled = true; }
    });
  }

  function prepareOptions(options) {
    return options.map((groupOption)=>{
      childrenGroup = groupOption[1]
      childrens = childrenGroup.map((childOption)=>({
        "id": childOption[1],
        "text": childOption[0]
      }))
      return { "text": groupOption[0], "children": childrens }
    })
  }

  function prepareResults(channelAndUsersOptions, userOptions, query){
    const channelOptions = channelAndUsersOptions.filter((option)=>(option['text'] !== 'Users'))
    let channelNameMatchingQuery =  channelOptions.map((channelObject)=>{
      const teamName = channelObject['text']
      const channels = channelObject['children']
      const filterChannels = channels.filter((channelName) => channelName.text.toLowerCase().includes(query.toLowerCase()))
      if(filterChannels.length == 0) { return }

      return { text: teamName, children: filterChannels }
    });

    channelNameMatchingQuery = channelNameMatchingQuery.filter((channel) => (channel))
    if(userOptions && userOptions[0].children.length > 0) { return channelNameMatchingQuery.concat(userOptions) }

    return channelNameMatchingQuery;
  }

  function fetchAndSetNotifyOptions(){
    const notifyToSelector = $("#notify-to-selecor");
    $.ajax({
      url: '<%= fetch_notify_options_notification_template_path(params[:id] || '-1') %>',
      success: (response) => {
        $('#notify-to-selecor')[0].removeAttribute('disabled');
        notifyToOptions = prepareOptions(response['data']);

        notifyToSelector.select2({
          data: notifyToOptions,
          ajax: {
            url: "<%= fetch_teams_users_notification_templates_path %>",
            type: "get",
            dataType: "json",
            delay: 250,
            data: (params) => ({ query: params.term }),
            processResults: function (response, params) {
              if(response['success'] == false) return { results : notifyToOptions }

              const userResponse = prepareOptions(response['users']);
              const results = prepareResults(notifyToOptions, userResponse, params.term);
              return { results }
            },
            cache: true
          }
        });
      },
      error: (response, status) => { $("#notify-options-alert").show(); }
    });
  }
</script>
