class Teams::SyncAgentMappingsJob < ApplicationJob
  sidekiq_options :retry => 3

  def perform(connected_account_id)
    @connected_account = ConnectedAccount.find(connected_account_id)
    @tenant = @connected_account.tenant

    unmapped_teams_users = Teams::FetchUsersService.new(@connected_account).get_unmapped_users
    initiate_mapping(@tenant.unmapped_users, unmapped_teams_users[:data]) if unmapped_teams_users[:success]

    @tenant.update_column(:agent_mapping_job, nil)
  end

  private

  def initiate_mapping(kylas_users, teams_users)
    teams_users.each do |teams_user|
      matching_kylas_user = kylas_users.detect{ |user| user[:email] == teams_user['mail'] }
      next if matching_kylas_user.nil?

      begin
        AgentMapping.create!(
          name: "#{teams_user['displayName']} (#{teams_user['mail']})",
          identifier: teams_user['id'],
          identifier_type: TEAMS,
          connected_account_id: @connected_account.id,
          tenant_id: @tenant.id,
          user_id: matching_kylas_user[:id]
        )
      rescue StandardError => e
        Rails.logger.error "Teams::SyncAgentMapping:: Error while creating agent mapping #{new_mapping.inspect} message #{e.message}"
      end
    end
  end
end
