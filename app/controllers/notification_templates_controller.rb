#frozen _string_literals: true

class NotificationTemplates<PERSON>ontroller < ApplicationController
  before_action :authenticate_user!, :kylas_api_key_present?
  before_action :load_and_validate_connected_account, except: [:index, :fetch_variables, :fetch_content]
  before_action :fetch_notification_template, only: [:edit, :update, :clone]
  before_action :assign_notification_template_attributes, only: [:create, :update]
  before_action :load_notification_template_variables, only: [:edit, :clone]

  def index
    @pagy, @notification_templates = pagy(current_tenant.notification_templates.includes(:notifiers).order('created_at DESC'))
  end

  def new
    @notification_template = current_tenant.notification_templates.build(connected_account: @connected_account, message_type: 'normal')
  end

  def create
    if @notification_template.save
      flash[:success] = I18n.t('notification_templates.saved_successfully')
      redirect_to notification_templates_path
    else
      flash[:danger] = I18n.t('notification_templates.failed_to_save')
      render :new
    end
  end

  def edit
    flash['alert'] = t('notification_templates.edit_template_warning')
  end

  def update
    ActiveRecord::Base.transaction do
      @notification_template.save!
      @notification_template.notifiers.destroy_all
      @notification_template.variable_type_mappings.destroy_all
      @notification_template.notifiers_attributes = send("build_#{@connected_account.account_type.downcase}_notifiers")
      @notification_template.variable_type_mappings_attributes = send("build_#{@connected_account.account_type.downcase}_variable_type_mappings")
      @notification_template.save!

      flash[:success] = t('notification_templates.saved_successfully')
      redirect_to notification_templates_path
    end
  rescue StandardError => e
    flash[:danger] = t('notification_templates.failed_to_save')
    flash[:danger] += e.message
    render :edit
  end

  def fetch_variables
    entity_type = params['entity_type']
    return { success: false } if entity_type.blank?
    return { success: false, error: I18n.t('fetch_entity_fields.invalid_entity_type') } if ALLOWED_ENTITIES.exclude? entity_type.upcase

    entity_field_response = prepare_entity_fields(entity_type)
    return { success: false, error: entity_field_response[:error] } unless entity_field_response[:success]

    entity_fields = entity_field_response[:entity_fields]
    respond_to do |format|
      format.json { render json: { success: true, entity_fields: entity_fields.to_json } }
      format.html
    end
  end

  def fetch_content
    notification_template = current_tenant.notification_templates.find_by_id(params.dig('template_id'))
    entity_fields = params[:entity_fields]
    return if notification_template.blank? || entity_fields.blank?

    content = Kylas::VariableProcessorService.new(JSON.parse(entity_fields), notification_template.content).internal_name_to_display_name
    respond_to do |format|
      format.json { render json: { success: true, content: CGI.unescapeHTML(content.to_s) }  }
      format.html
    end
  end

  def fetch_mention_users
    respond_to do |format|
      format.json { render json: Teams::TemplatePayload.new(@connected_account).mention_users(params[:query]) }
      format.html
    end
  end

  def fetch_teams_users_by_name
    respond_to do |format|
      format.json { render json: Teams::TemplatePayload.new(@connected_account).teams_user_by_name(params[:query]) }
      format.html
    end
  end

  def clone
    existing_template_attributes = @notification_template.attributes.with_indifferent_access
    @notification_template = current_tenant.notification_templates.build(existing_template_attributes.except(:id, :created_at, :updated_at, :title))
    @notification_template.title = existing_template_attributes[:title] + '(Copy)'
    @notification_template.clone = true

    render :new
  end

  def fetch_notify_options
    notify_options = prepare_notify_options

    respond_to do |format|
      format.json { render json: notify_options, status: notify_options[:status] }
      format.html
    end
  end

  private

  def permitted_params
    params
      .require(:notification_template)
      .permit(
        :title,
        :entity_type,
        :notifiers,
        :message_type,
        :heading,
        :content,
        :active
      )
  end

  def load_and_validate_connected_account
    @connected_account = current_tenant.connected_account
    if @connected_account.blank? || @connected_account&.status == INACTIVE
      flash[:danger] = I18n.t('notification_templates.connect_account_request')
      redirect_to connected_accounts_path and return
    end
  end

  def assign_notification_template_attributes
    entity_fields_params = params.dig(:notification_template, :entity_fields)
    if entity_fields_params.blank?
      flash[:danger] = t('notification_templates.unable_to_fetch_entity_fields')
      redirect_to notification_templates_path and return
    end

    @entity_fields = JSON.parse(entity_fields_params)
    @variables_and_heading_hash = variable_replace_display_name_to_internal(permitted_params[:heading])
    @variables_and_content_hash = variable_replace_display_name_to_internal(permitted_params[:content])

    if @notification_template.blank?
      @notification_template = current_tenant.notification_templates.build(connected_account: @connected_account, message_type: 'normal')
      @notification_template.notifiers_attributes = send("build_#{@connected_account.account_type.downcase}_notifiers")
      @notification_template.variable_type_mappings_attributes = send("build_#{@connected_account.account_type.downcase}_variable_type_mappings")
    end

    if params.dig(:notification_template, :notifiers).blank?
      flash[:danger] = t('notification_templates.notifiers_missing')
      redirect_to notification_templates_path and return
    end

    @notification_template.assign_attributes(permitted_params)
    @notification_template.view_entity_cta = true
    @notification_template.heading = @variables_and_heading_hash[:content]
    @notification_template.content = @variables_and_content_hash[:content]
  end

  def fetch_notification_template
    @notification_template = current_tenant.notification_templates.find_by_id(params[:id])
    if @notification_template.blank?
      flash[:danger] = I18n.t('notification_templates.not_found')
      redirect_to notification_templates_path and return
    end
  end

  def prepare_entity_fields(entity_type)
    entity_field_response = Kylas::FetchEntityFieldService.new(entity_type, current_tenant).fetch_field
    return { success: false, error: entity_field_response[:error] } unless entity_field_response[:success]

    entity_fields = entity_field_response[:data].map do |field|
      {
        'displayName' => field['displayName'],
        'name' => field['name'],
        'isStandard' => field['standard'],
        'type' => field['type']
      }
    end

    return { success: true, entity_fields: entity_fields }
  end

  def variable_replace_display_name_to_internal(content)
    Kylas::VariableProcessorService.new(@entity_fields, content).display_name_to_internal_name
  end

  def variable_replace_internal_name_to_display(content)
    Kylas::VariableProcessorService.new(@entity_fields, content).internal_name_to_display_name
  end

  def prepare_notify_options
    template_payload_response = (@connected_account.account_type.titleize.constantize)::TemplatePayload.new(@connected_account).prepare

    return { success: false, status: :bad_request } unless template_payload_response[:success]

    notification_template = current_tenant.notification_templates.find_by_id(params[:id])
    notify_options = get_relative_options + template_payload_response[:data]
    return { success: true, data: notify_options, status: :ok } if notification_template.nil?

    # We need to append the users that have already been selected by the users
    # and did not come in the API response since we are fetching a limited number of users in the first hit.
    @notifiers, unavailable_users = [], []
    users = notify_options.detect{ |options| options.first == USER.capitalize.pluralize }.second

    notification_template.notifiers.each do |notifier|
      notifier_value = [
        notifier['recipient_type'],
        notifier['recipient_name'],
        notifier['recipient_id']
      ].join(SEPERATOR)

      notifier_value += SEPERATOR + notifier['team_or_chat_id'] if notifier.recipient_type == CHANNEL
      @notifiers << notifier_value

      if notifier.recipient_type == USER
        if users.detect{ |user_name, user_value| user_value == notifier_value }.nil?
          unavailable_users << [notifier['recipient_name'], notifier_value]
        end
      end
    end

    return { success: true, data: notify_options } if unavailable_users.blank?

    notify_options = notify_options.map do |group_option, users|
      users += unavailable_users if group_option == USER.capitalize.pluralize
      [group_option, users]
    end

    { success: true, data: notify_options, status: :ok }
  end

  def load_notification_template_variables
    if ALLOWED_ENTITIES.exclude? @notification_template['entity_type']
      flash[:danger] = t('fetch_entity_fields.invalid_entity_type')
      redirect_to notification_templates_path and return
    else
      @selected_entity = @notification_template.entity_type.upcase
    end

    entity_fields_response = prepare_entity_fields(@notification_template['entity_type'])
    unless entity_fields_response[:success]
      flash[:danger] = entity_fields_response[:error]
      redirect_to notification_templates_path and return
    end

    @entity_fields = entity_fields_response[:entity_fields]
    @variables = @entity_fields.map{ |field| ["{{#{field['displayName']}}}", "{{#{field['displayName']}}, {if missing: <free text>}}"] }

    template_content = @notification_template.content
    template_heading = @notification_template.heading
    @heading = variable_replace_internal_name_to_display(template_heading)
    @content = variable_replace_internal_name_to_display(template_content)
    template_notifiers = @notification_template.notifiers

    return if template_notifiers.blank?

    @notify_options, @notifiers = [], []
    user_options, channel_options, relative_options = [], [], []

    @notification_template.notifiers.each do |notifier|
      case notifier.recipient_type
      when USER
        option_value = [USER, notifier.recipient_name, notifier.recipient_id].join(SEPERATOR)
        user_options << [notifier.recipient_name, option_value]
        @notifiers << option_value
      when CHANNEL
        option_value = [CHANNEL, notifier.recipient_name, notifier.recipient_id, notifier.team_or_chat_id].join(SEPERATOR)
        channel_options << [notifier.recipient_name, option_value]
        @notifiers << option_value
      when RELATIVE
        option_value = [RELATIVE, notifier.recipient_name, notifier.recipient_id].join(SEPERATOR)
        relative_options << [notifier.recipient_name, option_value]
        @notifiers << option_value
      end
    end

    @notify_options << [RELATIVE.titleize, relative_options] if relative_options.present?
    @notify_options << [CHANNEL.titleize, channel_options] if channel_options.present?
    @notify_options << [USER.titleize, user_options] if user_options.present?
  end

  # TODO: Move to some common service which could be used in Teams::TemplatePayload and Slack::TemplatePayload
  def get_relative_options
    [
      [
        RELATIVE.titleize,
        [
          [RELATIVE_OPTIONS[:record_owner], [RELATIVE, RELATIVE_OPTIONS[:record_owner], 'record_owner'].join(SEPERATOR)],
          [RELATIVE_OPTIONS[:record_created_by], [RELATIVE, RELATIVE_OPTIONS[:record_created_by], 'record_created_by'].join(SEPERATOR)],
          [RELATIVE_OPTIONS[:record_updated_by], [RELATIVE, RELATIVE_OPTIONS[:record_updated_by], 'record_updated_by'].join(SEPERATOR)]
        ]
      ]
    ]
  end

  def build_teams_notifiers
    params.dig(:notification_template, :notifiers).to_a.filter_map do |notifier|
      next if notifier.blank?

      recipient_type, recipient_name, recipient_id, team_or_chat_id = notifier.split(SEPERATOR)
      {
        recipient_type: recipient_type,
        recipient_name: recipient_name,
        recipient_id: recipient_id,
        team_or_chat_id: team_or_chat_id
      }
    end
  end

  def build_teams_variable_type_mappings
    (@variables_and_heading_hash[:variables_internal_names] + @variables_and_content_hash[:variables_internal_names]).uniq.map do |variable_internal_name|
      {
        name: variable_internal_name['name'],
        variable_type: variable_internal_name['type'],
      }
    end
  end
end
