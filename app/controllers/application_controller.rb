class ApplicationController < KylasEngine::ApplicationController
  include Pagy::Backend
  include Authentication
  before_action :store_location, :allow_iframe

  def append_info_to_payload(payload)
    super
    payload[:level] =
      case payload[:status]
      when 200
        'INFO'
      when 302
        'WARN'
      else
        'ERROR'
      end
    payload[:host] = request.host
    payload[:remote_ip] = request.remote_ip
    payload[:ip] = request.ip
    payload[:user_id] = current_user&.id
    payload[:tenant_id] = current_user&.tenant_id
    payload[:kylas_user_id] = current_user&.kylas_user_id
    payload[:kylas_tenant_id] = current_user&.tenant&.kylas_tenant_id
  end

  def kylas_api_key_present?
    if current_tenant.blank?
      flash[:danger] = t('sign_in_message')
      redirect_to new_user_session_path
      return
    end

    if current_tenant.kylas_api_key.blank?
      flash[:danger] = t('add_api_key')
      redirect_to edit_tenant_path(current_tenant)
      return
    end
  end

  private

  def allow_iframe
    response.headers['X-Frame-Options'] = 'ALLOW-FROM https://app-qa.sling-dev.com/, ALLOW-FROM https://app.kylas.io/, ALLOW-FROM https://crm.sell.do/, ALLOW-FROM https://app-qa.sell-do.com/'
  end

  def store_location
    if session[:previous_url].blank? || session[:previous_url] == '/'
      session[:previous_url] = request.fullpath if request.fullpath.include?('code') || request.fullpath.include?('fetch-or-build')
    end
    session[:previous_url]
  end
end
