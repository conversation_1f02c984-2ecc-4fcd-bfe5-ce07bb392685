# frozen_string_literal: true

class WorkflowActionsController < ApplicationController
  layout 'iframe_action'
  before_action :authenticate_user!, :kylas_api_key_present?, :validate_parameters
  protect_from_forgery with: :null_session, only: :deliver_via_workflow
  skip_before_action :verify_authenticity_token, :authenticate_user!, :kylas_api_key_present?, :validate_parameters, only: :deliver_via_workflow

  def fetch_or_build
    return if @errors.present?

    if @resourceId.present?
      @workflow_action = current_tenant.workflow_actions.find_by(id: @resourceId, entity_type: @entity_type)
      @errors << I18n.t('workflow_actions.not_found') and return if @workflow_action.blank?
    end

    entity_field_response = Kylas::FetchEntityFieldService.new(@entity_type, current_tenant).fetch_field
    @errors << entity_field_response[:error] and return unless entity_field_response[:success]
    @entity_fields = entity_field_response[:data].map do |field|
      {
        'displayName' => field['displayName'],
        'name' => field['name'],
        'isStandard' => field['standard']
      }
    end

    if @workflow_action.blank?
      fetch_notification_templates
      @workflow_action = current_tenant.workflow_actions.build()
    else
      fetch_notification_templates(template_id: @workflow_action.notification_template_id)
    end
  end

  def save
    return if @errors.present?

    return @errors << I18n.t('notification_templates.template_id_missing') if @template_id.blank?

    notification_template = current_tenant.notification_templates.find_by_id(@template_id)
    return @errors << I18n.t('notification_templates.not_found') if notification_template.blank?

    workflow_action = @resourceId.present? ?
      current_tenant.workflow_actions.find_by_id(@resourceId) :
      current_tenant.workflow_actions.build(user_id: current_user.id)
    return @errors << I18n.t('notification_templates.workflow_missing') if workflow_action.blank?

    workflow_action.assign_attributes(save_permitted_params)
    if workflow_action.save
      entity_field_response = Kylas::FetchEntityFieldService.new(@entity_type, current_tenant).fetch_field
      return @errors << entity_field_response[:error] unless entity_field_response[:success]

      @entity_fields = entity_field_response[:data]
      variables_in_heading = get_variables_in_content(notification_template.heading)
      variables_in_content = get_variables_in_content(notification_template.content)

      internal_names_in_heading_and_content = variables_in_content + variables_in_heading

      @workflow_payload = Kylas::BuildWorkflowPayloadService.new(
        workflow_action,
        internal_names_in_heading_and_content.uniq
      ).prepare

      respond_to do |format|
        format.json { render json: { success: true }  }
        format.html
      end
    else
      Rails.logger.error "Error while saving workflow action, Marketplace Tenant ID: #{current_tenant.id}, Errors: #{@workflow_action.errors.full_messages.join(', ')}"
      @errors << I18n.t('failure_and_contact_message', product_name: PRODUCT_NAME)
    end
  end

  def deliver_via_workflow
    SendNotificationJob.perform_later(params.permit!.to_h.with_indifferent_access)
    head :ok
  end

  private

  def fetch_notification_templates(template_id: nil)
    query = "(
      notification_templates.entity_type = '#{@entity_type}' AND
      notification_templates.active = true
    )"
    query << " OR (notification_templates.id = #{template_id})" if template_id.present?

    @notification_templates = current_tenant.notification_templates.where(query).select(:id, :title, :active, :content)

    if @notification_templates.blank?
      @errors << t(
        'notification_templates.active_templates_not_found',
        entity_type: @entity_type
      )
    end
  end

  def validate_parameters
    parsed_params = permitted_params
    @user_id = parsed_params[:userId] || parsed_params.dig(:workflow_action, :userId)
    @tenant_id = parsed_params[:tenantId] || parsed_params.dig(:workflow_action, :tenant_id)
    @entity_type = parsed_params[:entityType] || parsed_params.dig(:workflow_action, :entity_type)
    @identifier = parsed_params[:identifier] || parsed_params.dig(:workflow_action, :identifier)
    @resourceId = parsed_params[:resourceId] || parsed_params.dig(:workflow_action, :resourceId)
    @template_id = parsed_params.dig(:workflow_action, :notification_template_id)
    @errors = []
    @errors.push(I18n.t('workflow_actions.user_id_missing')) if @user_id.blank?
    @errors.push(I18n.t('workflow_actions.tenant_id_missing')) if @tenant_id.blank?
    @errors.push(I18n.t('workflow_actions.entity_type_missing')) if @entity_type.blank?
    @errors.push(I18n.t('workflow_actions.identifier_missing')) if @identifier.blank?
    @errors.push(I18n.t('connected_accounts.account_inactive')) if current_tenant.connected_account.blank? || current_tenant.connected_account&.status == INACTIVE
  end

  def permitted_params
    params.permit(
      :tenantId,
      :userId,
      :entityType,
      :identifier,
      :resourceId,
      workflow_action: [ :tenant_id, :userId, :entity_type, :identifier, :resourceId, :notification_template_id ]
    )
  end

  def save_permitted_params
    params
      .require(:workflow_action)
      .permit(
        :entity_type,
        :notification_template_id
      )
  end

  def get_variables_in_content(content)
    variables = content.scan(/({{[\s|\w|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]]+}, {if missing: [^{}]*[\s|\w]*}})/).flatten.map(&:strip).uniq
    return [] if variables.blank? || @entity_fields.blank?
    variables_in_content = []
    variables.each do |variable|
      actual_variable = variable.scan(/{{[\s|\w|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]]+},/).first
      valid_entity_field_variable = @entity_fields.find { |field| "{{#{field['name']}}," == actual_variable }
      next if valid_entity_field_variable.blank?
      variables_in_content << valid_entity_field_variable
    end
    variables_in_content
  end
end
