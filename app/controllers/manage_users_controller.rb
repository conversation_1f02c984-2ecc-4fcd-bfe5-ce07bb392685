# frozen_string_literal: true

class ManageUsersController < ApplicationController
  before_action :authenticate_user!, :authenticate_tenant, :kylas_api_key_present?
  before_action :load_and_validate_user, only: [:edit, :update]
  before_action :fetch_kylas_users_data, only: :edit

  def index
    @fetch_users_in_progress = current_tenant.fetch_and_create_users_job.present?
    @pagy, @users = pagy(current_tenant.users.order('updated_at DESC'))
  end

  def update
    if @user.update(user_params)
      flash[:success] = t('manage_users.success', action: 'updated')
      redirect_to manage_users_path
    else
      @kylas_users = fetch_kylas_users_data
      flash[:danger] = t('manage_users.failure', action: 'updated')
      render :edit
    end
  end

  def sync
    if current_tenant.fetch_and_create_users_job.present?
      flash[:danger] = I18n.t('manage_users.user_fetch_in_progress')
    else
      current_tenant.update_column(:fetch_and_create_users_job, FetchAndCreateUsersJob.perform_later(current_user).job_id)
      flash[:success] = I18n.t('manage_users.fetching_scheduled')
    end

    redirect_to manage_users_path
  end

  private

  def fetch_kylas_users_data
    user_response = Kylas::FetchUserDetailService.new(current_user).get_another_users_details(@user.kylas_user_id)
    redirect_to manage_users_path, flash: { danger: user_response[:error] } and return unless user_response[:success]

    user_name = "#{user_response[:details]['firstName']} #{user_response[:details]['lastName']}".strip
    @kylas_users = [
        [user_name, @user.id, { 'data-email' => @user.email, 'data-name' => user_name }]
      ]
  end

  def load_and_validate_user
    @user = current_tenant.users.find_by(id: params[:id])
    if @user.nil?
      redirect_to manage_users_path, flash: { danger: t('manage_users.invalid_user_provided') }
      return
    end
  end

  def user_params
    params.require(:user).permit(:name, :kylas_user_id)
  end
end
