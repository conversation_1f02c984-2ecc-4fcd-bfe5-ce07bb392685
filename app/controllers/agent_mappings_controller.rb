# frozen_string_literal: true

class AgentMappingsController < ApplicationController
  before_action :authenticate_user!, :authenticate_tenant, :kylas_api_key_present?
  before_action :load_and_validate_connected_account
  before_action :fetch_kylas_users, only: [:new, :edit]
  before_action :load_and_validate_agent_mapping, only: [:update, :destroy]

  def index
    @mapping_in_progress = current_tenant.agent_mapping_job.present?
    @pagy, @mappings = pagy(@connected_account.agent_mappings.includes(:user).order('created_at DESC'))
  end

  def new
    @agent_mapping = @connected_account.agent_mappings.build(identifier_type: @connected_account.account_type, tenant_id: current_tenant.id)
    @users = send("fetch_#{@agent_mapping.identifier_type.downcase}_users")
  end

  def create
    @agent_mapping = @connected_account.agent_mappings.build(permitted_params.merge(tenant_id: current_tenant.id))
    if @agent_mapping.save
      flash[:success] = t('agent_mapping.success', action: 'added')
      redirect_to connected_account_agent_mappings_path
    else
      @kylas_users = fetch_kylas_users
      @users = send("fetch_#{@agent_mapping.identifier_type.downcase}_users")
      render :new
    end
  end

  def edit
    @agent_mapping = @connected_account.agent_mappings.find(params[:id])
    @users = send("fetch_#{@agent_mapping.identifier_type.downcase}_users")

    kylas_user  = current_tenant.users.find(@agent_mapping.user_id)
    @users.unshift([@agent_mapping.name, @agent_mapping.identifier, { "data-name"=>@agent_mapping.name }])
    @kylas_users << [kylas_user.name, kylas_user.id]
  end

  def update
    @agent_mapping.assign_attributes(permitted_params)
    if @agent_mapping.save
      flash[:success] = t('agent_mapping.success', action: params[:action])
      redirect_to connected_account_agent_mappings_path
    else
      @kylas_users = fetch_kylas_users
      @users = send("fetch_#{@agent_mapping.identifier_type.downcase}_users")
      render :edit
    end
  end

  def destroy
    if @agent_mapping.destroy
      flash[:success] = t('agent_mapping.success', action: params[:action])
      redirect_to connected_account_agent_mappings_path
    else
      @connected_account = load_and_validate_connected_account
      @kylas_users = fetch_kylas_users
      @users = send("fetch_#{@agent_mapping.identifier_type.downcase}_users")
      flash[:danger] = t('agent_mapping.failure', action: params[:action])
      render :edit
    end
  end

  def autocomplete
    users_response = fetch_teams_users_by_name
    respond_to do |format|
      format.json { render json: { success: users_response[:success], users: users_response[:users] } }
      format.html
    end
  end

  def sync
    if current_tenant.agent_mapping_job.present?
      flash[:danger] = t('agent_mapping.mapping_in_progress')
    else
      current_tenant.update_column(:agent_mapping_job, (@connected_account.account_type.titleize.constantize)::SyncAgentMappingsJob.perform_later(@connected_account.id).job_id)
      flash[:success] = t('agent_mapping.schedule_sync')
    end

    redirect_to connected_account_agent_mappings_path
  end

  private

  def load_and_validate_connected_account
    # TODO: Since for now we have a single connected account, we are fetching the connected account directly from the tenant.
    # When we implement Slack integration, we will use the connected_account ID from the params to fetch the connected account.

    @connected_account = current_tenant.connected_account
    if @connected_account.nil?
      flash[:danger] = t('agent_mapping.account_not_connected')
      redirect_to connected_accounts_path and return
    end
  end

  def fetch_kylas_users
     @kylas_users = current_tenant.unmapped_users.map { |user| [user.name, user.id] }
  end

  def fetch_teams_users
    user_data = Teams::FetchUsersService.new(@connected_account).get_users
    mapped_users = @connected_account.agent_mappings.pluck(:identifier)
    if user_data[:success]
      response = []
      user_data[:users][0][1].each do |user|
        response << [user.first, user.second] if mapped_users.exclude?(user.second)
      end
      response
    else
      []
    end
  end

  def fetch_teams_users_by_name
    user_data = Teams::FetchUsersService.new(@connected_account).fetch_users_by_name(params[:query])
    mapped_users = @connected_account.agent_mappings.pluck(:identifier)

    if user_data[:success]
      response = []
      user_data[:users][0][1].each do |user|
        response << [user.first, user.second] if mapped_users.exclude?(user.second)
      end
      { success: true, users: response }
    else
      { success: false, users: [] }
    end
  end

  def permitted_params
    params
      .require(:agent_mapping)
      .permit(
        :identifier,
        :user_id,
        :identifier_type,
        :name
      )
  end

  def load_and_validate_agent_mapping
    @agent_mapping = @connected_account.agent_mappings.find_by(id: params[:id])

    if @agent_mapping.nil?
      flash[:danger] = t('agent_mapping.invalid_mapping_id')
      redirect_to connected_account_agent_mappings_path and return
    end
  end
end
