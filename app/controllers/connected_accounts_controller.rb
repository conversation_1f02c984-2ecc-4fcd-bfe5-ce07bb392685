class ConnectedAccountsController < ApplicationController
  before_action :authenticate_user!, :kylas_api_key_present?

  def index
    @connected_account = current_tenant.connected_account
  end

  def disconnect
    if current_tenant.connected_account.blank?
      flash[:danger] = t('connected_accounts.account_not_exist')
    else
      if current_tenant.connected_account.status == ACTIVE
        current_tenant.connected_account.status = INACTIVE
        flash[:danger] = t('connected_accounts.failed_to_disconnect') unless current_tenant.connected_account.save
        flash[:success] = t('connected_accounts.account_disconnected_successfully')
      else
        flash[:danger] = t('connected_accounts.account_already_inactive')
      end
    end

    redirect_to connected_accounts_path
  end

  def get_access_token
    parameters = get_token_params
    auth_code = parameters[:code]
    if auth_code.present?
      @connected_account = current_tenant.connected_account.blank? ?
        current_tenant.build_connected_account(user_id: current_user.id, account_type: TEAMS) :
        current_tenant.connected_account
      token_service_response = ConnectedAccountService.new(@connected_account, auth_code).get_token_and_save_account
      token_service_response[:success] ? flash[:success] = I18n.t('connected_accounts.successful') : flash[:danger] = token_service_response[:error]
    else
      flash[:danger] = I18n.t('connected_accounts.failed_to_connect')
    end
    redirect_to connected_accounts_path
  end

  private

  def get_token_params
    params.permit(:code, :error_description, :error)
  end
end
