# frozen_string_literal: true

KylasEngine::User.class_eval do

  has_one   :connected_account,  dependent: :destroy
  has_many  :workflow_actions,   dependent: :destroy

  attr_accessor :skip_password_validation

  def get_kylas_access_token
    return self.kylas_access_token if self.kylas_access_token_expires_at.to_i >= Time.now.to_i
    token_response = Kylas::FetchTokenService.new(self).get_token
    return nil unless token_response[:success]
    self.update(token_response[:value]) ? token_response[:value][:kylas_access_token] : nil
  end

  def password_required?
    return false if skip_password_validation

    super
  end
end
