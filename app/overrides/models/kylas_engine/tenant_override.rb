# frozen_string_literal: true

KylasEngine::Tenant.class_eval do

  has_many  :users,             dependent: :destroy
  has_one   :connected_account, dependent: :destroy
  has_many  :workflow_actions,  dependent: :destroy
  has_many  :notification_logs,  dependent: :destroy
  has_many  :notification_templates,  dependent: :destroy

  def unmapped_users
    users.where(Arel.sql("id NOT IN (SELECT user_id FROM agent_mappings WHERE tenant_id=#{id})"))
  end
end
