# frozen_string_literal: true

class DeviseMailer < Devise::Mailer
  def confirmation_instructions(record, token, opts={})
    mail = super
    mail.subject = t('devise.mailer.confirmation_instructions.subject', app_name: APP_NAME)
    mail
  end

  def reset_password_instructions(record, token, opts = {})
    mail = super
    mail.subject = t('devise.mailer.reset_password_instructions.subject', app_name: APP_NAME)
    mail
  end

  def unlock_instructions(record, token, opts = {})
    mail = super
    mail.subject = t('devise.mailer.unlock_instructions.subject', app_name: APP_NAME)
    mail
  end

  def email_changed(record, opts = {})
    mail = super
    mail.subject = t('devise.mailer.email_changed.subject', app_name: APP_NAME)
    mail
  end

  def password_change(record, opts = {})
    mail = super
    mail.subject = t('devise.mailer.password_change.subject', app_name: APP_NAME)
    mail
  end
end
