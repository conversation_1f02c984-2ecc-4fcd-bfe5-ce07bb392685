# frozen_string_literal: true

class CreateOrUpdateUser
  def initialize(user)
    @user = user
  end

  def process(users)
    return { success: false } if users.blank? || @user.nil?

    saved_users = KylasEngine::User.where(tenant_id: @user.tenant_id, kylas_user_id: users.map{ |user| user['id'] })
    users.each do |user|
      currently_saved_user = saved_users.detect{ |kylas_saved_user|  kylas_saved_user.kylas_user_id == user['id'] }
      updated_name = "#{user['firstName']} #{user['lastName']}".strip

      begin
        # Check if the user already exists in the database,
        # if so, update the details if there's a mismatch

        if currently_saved_user.present?
          if user['email'] != currently_saved_user.email || updated_name != currently_saved_user.name
            currently_saved_user.assign_attributes(email: user['email'], name: updated_name)
            currently_saved_user.skip_reconfirmation!
            currently_saved_user.save!
          end
          next
        end

        new_user = KylasEngine::User.new(
              email: user['email'],
              name: updated_name,
              kylas_user_id: user['id'],
              tenant_id: @user.tenant_id
            )
        new_user.skip_password_validation = true
        new_user.skip_confirmation_notification!
        new_user.save!
      rescue StandardError => e
        Rails.logger.error "CreateOrUpdateUser:: Error while creating user #{user.inspect} message #{e.message}"
      end
    end

    { success: true }
  end
end
