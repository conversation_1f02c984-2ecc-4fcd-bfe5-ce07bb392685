# frozen_string_literal: true

class ConnectedAccountService
  def initialize(connected_account, auth_code)
    @connected_account = connected_account
    @auth_code = auth_code
  end

  def get_token_and_save_account
    url = URI(MICROSOFT_TOKEN_URL)
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Post.new(url)
    request['Content-Type'] = 'application/x-www-form-urlencoded'
    request.body = URI.encode_www_form(
      [
        ['client_id', Rails.application.credentials.notifier_app.client_id],
        ['client_secret', Rails.application.credentials.notifier_app.client_secret],
        ['redirect_uri', Rails.application.credentials.notifier_app.redirect_url],
        ['scope', MICROSOFT_AUTH_SCOPE],
        ['grant_type', 'authorization_code'],
        ['code', @auth_code]
      ]
    )
    begin
      response = https.request(request)
      if response.code == '200'
        parsed_token_response = JSON.parse(response.body)
        id_token = parsed_token_response['id_token']
        decrypted_token = JWT.decode(id_token, nil, false)
        user_info_hash = decrypted_token.detect { |key| key['email'] }
        return { success: false, error: I18n.t('connected_accounts.email_mismatched') } if @connected_account.email.present? && @connected_account.email != user_info_hash['email']

        @connected_account.assign_attributes(
          {
            name: user_info_hash['name'],
            email: user_info_hash['email'],
            status: ACTIVE,
            microsoft_user_id: user_info_hash['oid'],
            access_token: parsed_token_response['access_token'],
            refresh_token: parsed_token_response['refresh_token'],
            expires_at: parsed_token_response['expires_in'].seconds.from_now
          }
        )
        if @connected_account.save
          { success: true }
        else
          { success: false, error: @connected_account.errors.full_messages.join(', ') }
        end
      else
        { success: false, error: fetch_errors(JSON.parse(response.body)) }
      end
    rescue StandardError => e
      Rails.logger.error "ConnectAccountService: get_tokens: Errors -> #{e.message}"
      { success: false, error: I18n.t('connected_accounts.failed_to_connect') }
    end
  end

  private

  def fetch_errors(parsed_resp)
    error_message = I18n.t('connected_accounts.failed_to_connect')
    error_message += "Error Codes: #{parsed_resp['error_codes'].join(', ')} " if parsed_resp['error_codes'].present?
    error_message += parsed_resp['error_description'] if parsed_resp['error_description'].present?
    error_message
  end
end
