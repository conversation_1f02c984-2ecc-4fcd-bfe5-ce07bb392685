# frozen_string_literal: true

class Teams::FetchChannelService
  def initialize(connected_account)
    @connected_account = connected_account
  end

  def fetch_channels
    access_token = @connected_account.get_access_token
    if access_token.blank?
      return { success: false, error: I18n.t('failure_and_contact_message', product_name: PRODUCT_NAME) }
    end

    teams_response = fetch_teams(access_token)
    return teams_response unless teams_response[:success]

    user_teams = teams_response[:teams].map { |team| { displayName: team['displayName'], id: team['id'] } }
    get_channels_from_teams(user_teams, access_token)
  end

  private

  def fetch_errors(parsed_resp)
    error_message = I18n.t('microsoft_service.channels_fetch_failed')
    error_message += " Error Code: #{parsed_resp['error']['code']}," if parsed_resp['error']['code'].present?
    error_message += " Error Description: #{parsed_resp['error']['message']}" if parsed_resp['error']['message'].present?
    error_message
  end

  def fetch_teams(access_token)
    url = URI(MICROSOFT_FETCH_TEAMS_URI)
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Get.new(url)
    request['Authorization'] = "Bearer #{access_token}"
    begin
      response = https.request(request)
      unless response.code == '200'
        return { success: false, error: fetch_errors(JSON.parse(response.body)) }
      end

      { success: true, teams: JSON.parse(response.body)['value'] }
    rescue StandardError => e
      Rails.logger.error "FetchChannelService: fetch_teams: Errors -> #{e.message}"
      { success: false, error: e.message }
    end
  end

  def get_channels_from_teams(user_teams, token)
    user_channels = []
    user_teams.each do |team|
      channel_response = get_channel_details(team[:id], token)
      return channel_response if channel_response[:success] == false

      channel_option = ["#{TEAMS.singularize.titleize} - #{team[:displayName]}"]
      channels = channel_response[:channel]
      channel_object = channels.map do |channel|
        [
          channel['displayName'],
          channel['id'],
          { 'team-id' => team[:id], 'recipient-type' => CHANNEL }
        ]
      end
      channel_option << channel_object
      user_channels << channel_option
    end
    { success: true, channel: user_channels }
  end

  def get_channel_details(team_id, token)
    url = URI("#{MICROSOFT_CHANNEL_BASE_URI}/#{team_id}/channels")
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Get.new(url)
    request['Authorization'] = "Bearer #{token}"
    begin
      response = https.request(request)

      return { success: false, error: fetch_errors(JSON.parse(response.body)) } if response.code != '200'

      { success: true, channel: JSON.parse(response.body)['value'] }
    rescue StandardError => e
      Rails.logger.error "FetchChannelService: get_channel_details: Errors -> #{e.message}"
      { success: false, error: I18n.t('microsoft_service.channels_fetch_failed') }
    end
  end
end
