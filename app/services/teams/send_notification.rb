class Teams::SendNotification
  def initialize(tenant, workflow_action, params)
    @mentions = []
    @tenant = tenant
    @workflow_action = workflow_action
    @notification_template = @workflow_action.notification_template
    @params = params.with_indifferent_access
    @teams_notification_heading = teams_notification_content(@notification_template.heading)
    @teams_notification_content = teams_notification_content(@notification_template.content)
    @params = @params.merge(tenant_id: @tenant.id)
  end

  def process
    @notification_template.notifiers.each do |notifier|
      url = URI(prepare_request_url(notifier))
      https = Net::HTTP.new(url.host, url.port)
      https.use_ssl = true
      request = Net::HTTP::Post.new(url)
      request["Authorization"] = "Bearer #{@tenant.connected_account.get_access_token}"
      request["Content-Type"] = "application/json"
      request.body = prepare_notification_body.to_json
      notification_log = @tenant.notification_logs.build
      begin
        response = https.request(request)
        if response.code == '201'
          notification_log.assign_attributes(prepare_notification_log_parameters(notifier.recipient_name, SUCCESS))
          notification_log.save!
        else
          parsed_response = JSON.parse(response.body)

          # We get blank message when required parameters are not passed in request url
          error_message = parsed_response['error']['message'].presence || I18n.t('failure_message')
          notification_log.assign_attributes(prepare_notification_log_parameters(notifier.recipient_name, FAILED, "Error: #{error_message}" ))
          notification_log.save!

          Rails.logger.error "Teams::SendNotification: send: Errors -> #{parsed_response}"
        end
      rescue StandardError => e
        notification_log.assign_attributes(prepare_notification_log_parameters(notifier.recipient_name, FAILED, e.message))
        Rails.logger.error "Teams::SendNotification: send: #{notification_log.errors.full_messages.join(', ')}" unless notification_log.save
        Rails.logger.error "Teams::SendNotification: send: Errors -> #{e.message}"
      end
    end
  end

  private

  def teams_notification_content(content)
    variables = content.scan(/({{[\s|\w|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]]+}, {if missing: [^{}]*[\s|\w]*}})/).flatten.map(&:strip).uniq
    @variable_processor = Kylas::VariableProcessorService.new

    variables.each { |variable_in_content| content = replace_internal_name_to_actual_value(variable_in_content, content) } unless variables.blank?

    mentions = content.scan(/\[\[@[^<]+?&lt;!-- \[\[[^\]]+\]\] --&gt;\]\]/).flatten.map(&:strip)
    return content if mentions.blank?

    mentions.each_with_index{ |mention_in_content, index| content = replace_and_assign_mentions(mention_in_content, content, index) }
    content
  end

  def replace_internal_name_to_actual_value(variable_in_content, content)
    #! Define Kylas::VariableProcessorService instance before using this method

    actual_variable = variable_in_content.scan(/{{[\s|\w|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]]+},/).first
    fall_back_variable = variable_in_content.scan(/if missing: [^{}]*[\s|\w]*}/).first
    actual_internal_name = actual_variable.gsub(/[{},]/, '')
    actual_fallback_value = fall_back_variable.gsub(/^.*: |}$/,'')
    field_value = @params.dig("variable_#{actual_internal_name}")

    field_value.present? ?
      content.gsub(variable_in_content, @variable_processor.process_variable_based_on_type(field_value, actual_internal_name, @notification_template.variable_type_mappings, @workflow_action.user).to_s) :
      content.gsub(variable_in_content, actual_fallback_value.include?('free text') ? '' : actual_fallback_value)
  end

  def replace_and_assign_mentions(mention_in_content, content, index)
    user_name, user_id = mention_in_content.scan(/\[\[@([^<&]+)&lt;!-- \[\[([^]]+)\]\]/).first
    return content if (user_name.blank? || user_id.blank?)

    entity_type, actual_user_id = user_id.split(SEPERATOR)

    content, mention = send("replace_#{entity_type}_mentions", index, actual_user_id, user_name, mention_in_content, content)

    @mentions << mention if mention.present?
    content
  end

  def replace_user_mentions(index, user_id, user_name, mention_in_content, content)
    [
      content.sub(mention_in_content, "<at id=#{index}>#{user_name}</at>"),
      {
        id: index,
        mentionText: user_name,
        mentioned: { user: { displayName: user_name, id: user_id } }
      }
    ]
  end

  def replace_relative_mentions(index, user_id, user_name, mention_in_content, content)
    kylas_user_id = @params[user_id.to_sym]
    return content unless kylas_user_id.present?

    kylas_user = @tenant.users.find_by(kylas_user_id: kylas_user_id)
    mapping = AgentMapping.find_by(tenant_id: @tenant.id, user_id: kylas_user.id, identifier_type: TEAMS)
    return  content.sub(mention_in_content, kylas_user.name) unless mapping.present?

    [
      content.sub(mention_in_content, "<at id=#{index}>#{kylas_user.name}</at>"),
      {
        id: index,
        mentionText: kylas_user.name,
        mentioned: { user: { displayName: kylas_user.name, id: mapping.identifier } }
      }
    ]
  end

  def prepare_notification_body
    {
      subject: @teams_notification_heading,
      importance: @notification_template.message_type,
      body: {
        contentType: "html",
        content: CGI.unescapeHTML(prepare_notification_content_html)
      },
      mentions: @mentions
    }
  end

  def prepare_request_url(notifier)
    case notifier.recipient_type
    when CHANNEL
      "#{MICROSOFT_CHANNEL_BASE_URI}/#{notifier.get_team_or_chat_id}/channels/#{notifier.recipient_id}/messages"
    when USER, RELATIVE
      "#{MICROSOFT_CHAT_URI}/#{notifier.get_team_or_chat_id(@params)}/messages"
    end
  end

  def prepare_view_entity_url
    case @workflow_action.entity_type
    when DEAL, LEAD, CONTACT
      "#{WEB_APP_URL}/sales/#{self.entity_type.downcase.pluralize}/details/#{entity_id}"
    when CALL_LOG
      "#{WEB_APP_URL}/sales/calls/list?id=#{entity_id}"
    when MEETING
      "#{WEB_APP_URL}/sales/meetings/list?id=#{entity_id}"
    else
      "#"
    end
  end

  def prepare_notification_content_html
    @teams_notification_content + "<hr/><p style='font-size: 12px; color: #687790;'>#{I18n.t('notification_templates.default_notification_message')}</p>"
  end

  def prepare_notification_log_parameters(recipient_name, status, error_message = nil)
    {
      entity_id: @params['entityId'],
      entity_type: @workflow_action.entity_type,
      status: status,
      recipient_name: recipient_name,
      error_message: error_message,
      account_name: @tenant.connected_account.name,
      template_name: @notification_template.title,
      notification_content: @teams_notification_content
    }
  end
end
