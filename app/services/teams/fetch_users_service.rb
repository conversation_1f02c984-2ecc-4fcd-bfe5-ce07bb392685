# frozen_string_literal: true

class Teams::FetchUsersService
  def initialize(connected_account, notification_template = nil)
    @connected_account = connected_account
    @notification_template = notification_template
  end

  def get_users
    access_token = @connected_account.get_access_token
    return { success: false, error: I18n.t('failure_and_contact_message', product_name: PRODUCT_NAME) } if access_token.blank?

    url = URI(MICROSOFT_FETCH_USERS_URI)
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Get.new(url)
    request["Authorization"] = "Bearer #{access_token}"
    begin
      response = https.request(request)
      return { success: false, error: fetch_errors(JSON.parse(response.body)) } unless response.code == '200'

      user_response = JSON.parse(response.body)['value']
      users = user_response.map{ |user|  user['id'] != @connected_account['microsoft_user_id'] ? ["#{user['displayName']} (#{user['mail']})", user['id'], { 'recipient-type': USER, name: user['displayName'] }] : nil }
      { success: true, users: [['Users', users.compact]] }
    rescue StandardError => e
      Rails.logger.error "FetchUsersService: fetch_users: Errors -> #{e.message}"
      { success: false, error: e.message }
    end
  end

  def get_chat_id(recipient_id)
    access_token = @connected_account.get_access_token
    return { success: false, error: I18n.t('failure_and_contact_message', product_name: PRODUCT_NAME) } if (access_token.blank? || recipient_id.blank?)

    url = URI(MICROSOFT_CHAT_URI)
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Post.new(url)
    request["Authorization"] = "Bearer #{access_token}"
    request["Content-Type"] = "application/json"
    request.body = request_body_for_create_chat(recipient_id).to_json
    begin
      response = https.request(request)
      return { success: false, error: fetch_errors(JSON.parse(response.body)) } unless response.code == '201'

      chat_id = JSON.parse(response.body)['id']
      { success: true, chat_id: chat_id }
    rescue StandardError => e
      Rails.logger.error "FetchUsersService: get_chat_id: Errors -> #{e.message}"
      { success: false, error: e.message }
    end
  end

  def fetch_users_by_name(query)
    return { success: false, error: I18n.t('notification_templates.query_parameter_missing') } if query.blank?

    access_token = @connected_account.get_access_token
    return { success: false, error: I18n.t('failure_and_contact_message', product_name: PRODUCT_NAME) } if access_token.blank?

    url = URI("#{MICROSOFT_FETCH_USERS_URI}?$search=\"displayName:#{query}\"")
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Get.new(url)
    request["Authorization"] = "Bearer #{access_token}"
    request["ConsistencyLevel"] = "eventual"
    begin
      response = https.request(request)
      return { success: false, error: fetch_errors(JSON.parse(response.body)) } unless response.code == '200'
      user_response = JSON.parse(response.body)['value']
      users = user_response.map{ |user| user['id'] != @connected_account['microsoft_user_id'] ? ["#{user['displayName']} (#{user['mail']})", user['id'], { 'recipient-type': USER, name: user['displayName'] }] : nil }
      { success: true, users: [['Users', users.compact]] }
    rescue StandardError => e
      Rails.logger.error "FetchUsersService: fetch_users_by_name: Errors -> #{e.message}"
      { success: false, error: e.message }
    end
  end

  def get_all_users
    @access_token = @connected_account.get_access_token
    return { success: false } if @access_token.blank?

    code, parsed_body = process_all_users_request
    users, skip_token = parse_user_response(parsed_body)

    while code == '200' && skip_token.present?
      code, parsed_body = process_all_users_request(skip_token)
      user_response, skip_token = parse_user_response(parsed_body)
      users += user_response unless user_response.blank?
    end

    { success: code == '200', data: users }
  end

  def get_unmapped_users
    user_data = get_all_users

    if user_data[:success]
      mapped_users = @connected_account.agent_mappings.pluck(:identifier)
      { success: true, data: user_data[:data].select { |user| mapped_users.exclude?(user['id']) } }
    else
      { success: false }
    end
  end

  private

  def process_all_users_request(skip_token = nil)
    url = MICROSOFT_FETCH_USERS_URI
    url += "?$skiptoken=#{skip_token}" unless skip_token.nil?
    url = URI(url)
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Get.new(url)
    request["Authorization"] = "Bearer #{@access_token}"
    begin
      response = https.request(request)

      [response.code, JSON.parse(response.body)]
    rescue StandardError => e
      Rails.logger.error "FetchUsersService: fetch_users: Errors -> #{e.message}"
      ['500']
    end
  end

  def parse_user_response(parsed_body)
    # We will get parsed_body as nil when there is error in process_all_users_request method
    return [] if parsed_body.nil?

    next_link, users = parsed_body['@odata.nextLink'], parsed_body['value']
    if next_link.present?
      uri = Hash[URI.decode_www_form(URI.parse(next_link).query)]
      return [users, uri['$skiptoken']]
    end
    return [users]
  end

  def fetch_errors(parsed_resp)
    error_message = 'Failed to fetch users, please try again later '
    error_message += "Error Code: #{parsed_resp['error']['code']} " if parsed_resp['error']['code'].present?
    error_message += "Error Description: #{parsed_resp['error']['message']}" if parsed_resp['error']['message'].present?
    error_message
  end

  def request_body_for_create_chat(recipient_id)
    {
      "chatType": "oneOnOne",
      "members": [
        {
          "@odata.type": "#microsoft.graph.aadUserConversationMember",
          "roles": [ "owner" ],
          "<EMAIL>": "https://graph.microsoft.com/#{MICOROSOFT_API_VERSION_1}/users/#{@connected_account.microsoft_user_id}"
        },
        {
          "@odata.type": "#microsoft.graph.aadUserConversationMember",
          "roles": [ "owner" ],
          "<EMAIL>": "https://graph.microsoft.com/#{MICOROSOFT_API_VERSION_1}/users/#{recipient_id}"
        }
      ]
    }
  end
end
