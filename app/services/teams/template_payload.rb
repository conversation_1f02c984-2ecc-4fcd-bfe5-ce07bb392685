# frozen_string_literal: true

class Teams::TemplatePayload
  def initialize(connected_account)
    @connected_account = connected_account
  end

  def prepare
    @channel_response = Teams::FetchChannelService.new(@connected_account).fetch_channels

    @user_response =
      if @channel_response[:success]
        Teams::FetchUsersService.new(@connected_account).get_users
      else
        { success: false }
      end

    unless @channel_response[:success] && @user_response[:success]
      return { success: false, message: "#{@channel_response[:error]} #{@user_response[:error]}".strip }
    end

    { success: true, data: format_channel_response + format_user_response }
  end

  def teams_user_by_name(query)
    @user_response = Teams::FetchUsersService.new(@connected_account).fetch_users_by_name(query)
    return { success: false, message: @user_response[:error] } unless @user_response[:success]

    { success: true, users: format_user_response }
  end

  def mention_users(query)
    @user_response = Teams::FetchUsersService.new(@connected_account).fetch_users_by_name(query)
    return { success: false, message: @user_response[:error] } unless @user_response[:success]

    { success: true, users: format_mention_response + fetch_and_assign_relative_response(query) }
  end

  private

  def format_channel_response
    # [["Channel - Kylas", ["channel|*|Monitoring-Apps|*|19:<EMAIL>|*|43dd-bdf5-2f0e89c4ec50"]]]

    @channel_response[:channel].map do |channel_name, channel_groups|
      [
        channel_name,
        channel_groups.map do |group|
          [
            group[0],
            [CHANNEL, group[0], group[1], group[2]['team-id']].join(SEPERATOR)
          ]
        end
      ]
    end
  end

  def format_user_response
    # [["Users", ["user|*|John Doe (<EMAIL>)|*|3447-49c4-9561-c3bd589c22893447|*|"]]]

   @user_response[:users].map do |title, users|
      [
        title,
        users.map do |user|
          [
            user[0],
            [USER, user[0], user[1]].join(SEPERATOR)
          ]
        end
      ]
    end
  end

  def format_mention_response
    users = @user_response[:users][0][1].map do |user|
      user_id = [USER, user[1]].join(SEPERATOR)

      {
        id: "[[@#{user[2][:name]}<!-- [[#{user_id}]] -->]]",
        text: user[0]
      }
    end
  end

  def fetch_and_assign_relative_response(query)
    users = []
    RELATIVE_OPTIONS.keys.each do |entity|
      matching_case = entity.to_s.include?(query.downcase)
      next unless entity.to_s.include?(query.downcase)

      user_id = [RELATIVE, entity].join(SEPERATOR)
      users << {
        id: "[[@#{RELATIVE_OPTIONS[entity]}<!-- [[#{user_id}]] -->]]",
        text: RELATIVE_OPTIONS[entity]
      }
    end
    users
  end
end
