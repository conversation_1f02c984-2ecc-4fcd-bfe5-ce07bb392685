class Teams::FetchNewTokenService
  def initialize(connected_account)
    @connected_account = connected_account
  end

  def get_token
    return { success: false } if @connected_account.blank? || @connected_account.refresh_token.blank?
    url = URI(MICROSOFT_TOKEN_URL)
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Post.new(url)
    request["Content-Type"] = "application/x-www-form-urlencoded"
    request.body = URI.encode_www_form(
      [
        ['client_id', Rails.application.credentials.notifier_app.client_id],
        ['client_secret', Rails.application.credentials.notifier_app.client_secret],
        ['grant_type', "refresh_token"],
        ['refresh_token', @connected_account.refresh_token]
      ]
    )
    begin
      response = https.request(request)
      return { success: false } unless response.code == '200'
      parsed_response = JSON.parse(response.body)
      {
        success: true,
        value: {
          access_token: parsed_response['access_token'],
          refresh_token: parsed_response['refresh_token'],
          expires_at: parsed_response['expires_in'].seconds.from_now
        }
      }
    rescue StandardError => e
      Rails.logger.error "FetchTokenService: get_token: Errors -> #{e.message}"
      { success: false }
    end
  end
end
