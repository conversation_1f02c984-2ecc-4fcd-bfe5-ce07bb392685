# frozen_string_literal: true

class Teams::MapKylasUsers
  def initialize users
    @users = users
  end

  def process
    unmapped_users = KylasEngine::User.where(teams_email: nil)
    @users.each do |team_user|
      team_user_email = team_user['mail']
      target_user = unmapped_users.detect{ |user| user.email == team_user_email }
      target_user.update_column(:teams_email, team_user_email) if target_user.present?
    end
  end
end
