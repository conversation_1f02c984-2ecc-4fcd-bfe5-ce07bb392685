# frozen_string_literal: true

class Kylas::VariableProcessorService
  def initialize(entity_fields = nil, content = nil)
    @entity_fields = entity_fields
    @content = content
    @variables = get_variables_in_content
  end

  def display_name_to_internal_name
    return { content: @content, variables_internal_names: [] } if @variables.blank? || @entity_fields.blank?

    variables_internal_names = []
    @variables.each do |variables_in_content|
      internal_name_replace_response = get_internal_name_and_replace_content(variables_in_content)
      next unless internal_name_replace_response[:success]

      variables_internal_names << internal_name_replace_response[:internal_name]
      @content = internal_name_replace_response[:content]
    end
    { content: @content, variables_internal_names: variables_internal_names }
  end

  def internal_name_to_display_name
    return @content if @variables.blank? || @entity_fields.blank?
    @variables.each { |variables_in_content| @content = replace_internal_name_to_display_name(variables_in_content) }
    @content
  end

  def process_variable_based_on_type(value, internal_name, mappings, user)
    @value, @internal_name, @user = value, internal_name, user
    return @value if (mappings.blank? || @value.nil? || @value == '')

    variable_type = mappings.detect { |mapping| mapping.name == internal_name }[:variable_type].to_s.downcase

    private_methods.include?("prepare_#{variable_type}_variable".to_sym) ?
      send("prepare_#{variable_type}_variable") :
      value
  end

  private

  def get_variables_in_content
    return [] if @content.blank?
    @content.scan(/({{[\s|\w|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]]+}, {if missing: [^{}]*[\s|\w]*}})/).flatten.map(&:strip).uniq
  end

  def get_internal_name_and_replace_content(variables_in_content)
    actual_variable = variables_in_content.scan(/{{[\s|\w|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]]+},/).first
    return { success: false } unless actual_variable.present?
    valid_entity_field_variable = @entity_fields.find { |field| "{{#{field['displayName']}}," == actual_variable }
    return { success: false } unless valid_entity_field_variable.present?

    content = @content.gsub(actual_variable, "{{#{valid_entity_field_variable['name']}},")
    { success: true, content: content, internal_name: valid_entity_field_variable }
  end

  def replace_internal_name_to_display_name(variables_in_content)
    actual_variable = variables_in_content.scan(/{{[\s|\w|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]]+},/).first
    valid_entity_field_variable = @entity_fields.find { |field| "{{#{field['name']}}," == actual_variable }
    return @content if valid_entity_field_variable.blank?
    content = @content.gsub(actual_variable, "{{#{valid_entity_field_variable['displayName']}},")
  end

  def prepare_email_variable
    @value.is_a?(Array) ? @value.join(', ') : @value
  end

  def prepare_meeting_invitees_variable
    @value.is_a?(Array) ?
      @value.map{ |participant| participant['name'] }.join(', ') :
      @value['name']
  end

  def prepare_datetime_picker_variable
    @user_details ||= Kylas::FetchUserDetailService.new(@user).get_details
    @date_time_format ||= JSON.parse File.read(Rails.root.join('config', 'standard', 'date-time-format.json'))
    @user_time_zone ||= @user_details[:success] ? @user_details[:details][:timezone] : "Asia/Mumbai"
    user_date_format = @user_details[:success] ? @user_details[:details][:dateFormat] : "MMM D, YYYY [at] h:mm a"

    @value = @value.in_time_zone(@user_time_zone)
    @value.strftime(@date_time_format[user_date_format])
  end

  def prepare_date_picker_variable
    @user_details ||= Kylas::FetchUserDetailService.new(@user).get_details
    @date_format ||= JSON.parse File.read(Rails.root.join('config', 'standard', 'date-format.json'))
    @user_time_zone ||= @user_details[:success] ? @user_details[:details][:timezone] : "Asia/Mumbai"
    user_date_format = @user_details[:success] ? @user_details[:details][:dateFormat] : "MMM D, YYYY [at] h:mm a"

    @value = @value.in_time_zone(@user_time_zone)
    @value.strftime(@date_format[user_date_format])
  end

  def prepare_phone_variable
    if @value.is_a?(Array)
      @value.map { |phone| "#{phone['dialCode']}#{phone['value']}".strip }.join(', ')
    else
      "#{@value['dialCode']}#{@value['value']}".strip
    end
  end

  def prepare_meeting_organizer_variable
    @value['name']
  end

  def prepare_look_up_variable
    if @value.is_a?(String)
      case @internal_name
      when 'timezone'
        @timezone_mapping ||= JSON.parse File.read(Rails.root.join('config', 'standard', 'timezone.json'))
        return @timezone_mapping[@value] || @value
      when 'requirementCurrency'
        @currency_mapping ||= JSON.parse File.read(Rails.root.join('config', 'standard', 'currency.json'))
        return @currency_mapping[@value] || @value
      when 'companyBusinessType'
        @business_type_mapping ||= JSON.parse File.read(Rails.root.join('config', 'standard', 'business-type.json'))
        return @business_type_mapping[@value] || @value
      when 'companyCountry', 'country'
        @country_mapping ||= JSON.parse File.read(Rails.root.join('config', 'standard', 'country.json'))
        return @country_mapping[@value] || @value
      when 'companyIndustry'
        @industry_mapping = JSON.parse File.read(Rails.root.join('config', 'standard', 'industry.json'))
        return @industry_mapping[@value] || @value
      else
        return @value
      end
    end

    return @value['name'] if @value.is_a?(Hash) && @value['name'].present?

    if @value.is_a?(Array)
      return @value.join(', ') if @value.first.is_a?(String)

      if @value.first.is_a?(Hash)
        return @value.map { |field| field['name'] }.join(', ') if @value.first['name'].present?
        return @value.map { |field| "#{field['firstName']} #{field['lastName']}".strip }.join(', ') if @value.first['firstName'].present?
      end
    end

    @value
  end

  def prepare_toggle_variable
    # CRM sends blank text for dnd if false so handling this here
    [true, 'true'].include?(@value)
  end

  def prepare_entity_lookup_variable
    return @value[:name] if @value.is_a?(Hash)

    @value.map { |field| field[:name] }.join(', ') if @value.is_a?(Array)
  end

  def prepare_file_picker_variable
    @value[:url]
  end

  alias :prepare_pick_list_variable :prepare_look_up_variable
  alias :prepare_multi_picklist_variable :prepare_look_up_variable
end
