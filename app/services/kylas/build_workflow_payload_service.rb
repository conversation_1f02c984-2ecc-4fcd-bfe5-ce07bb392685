class Kylas::BuildWorkflowPayloadService
  def initialize(workflow_action, variables)
    @workflow_action = workflow_action
    @variables = variables
  end

  def prepare
    return nil if @workflow_action.blank?
    {
      "name": 'Send notification on teams',
      "description": 'This is used for sending automated notification in teams via workflow',
      "method": 'POST',
      "resourceId": @workflow_action.id.to_s,
      "requestUrl": api_request_url,
      "parameters": prepare_parameters
    }.with_indifferent_access
  end

  private

  def api_request_url
    "https://#{Rails.configuration.action_mailer.default_url_options[:host]}/workflow-actions/#{@workflow_action.tenant.webhook_api_key}/deliver-via-workflow.json"
  end

  def prepare_parameters
    parameters = [
      {
        "name": 'resourceId',
        "entity": 'CUSTOM',
        "attribute": @workflow_action.id,
        "isStandard": false
      },
      {
        "name": 'entityId',
        "entity": @workflow_action.entity_type,
        "attribute": 'id',
        "isStandard": true
      },
      {
        "name": "record_created_by",
        "entity": "CREATED_BY",
        "attribute": "id",
        "isStandard": true
      },
      {
        "name": "record_updated_by",
        "entity": "UPDATED_BY",
        "attribute": "id",
        "isStandard": true
      }
    ]

    @variables.each do |field|
      parameters << {
        "name": "variable_#{field['name']}",
        "entity": @workflow_action.entity_type,
        "attribute": field['name'],
        "isStandard": field['standard']
      }
    end

    add_required_fields(parameters)
  end

  def add_required_fields(parameters)
    case @workflow_action.entity_type.singularize.upcase

    when CALL_LOG
      parameters << {
        "name": "record_owner",
        "entity": "LOGGED_BY",
        "attribute": "id",
        "isStandard": true
      }
    else
      parameters << {
        "name": "record_owner",
        "entity": "#{@workflow_action.entity_type}_OWNER",
        "attribute": "id",
        "isStandard": true
      }
    end

    parameters.uniq
  end
end
