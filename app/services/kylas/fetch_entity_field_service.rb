# frozen_string_literal: true

class Kylas::FetchEntityFieldService
  def initialize(entity_type, current_tenant)
    @entity_type = entity_type
    @current_tenant = current_tenant
  end

  def fetch_field
    send "#{@entity_type.singularize.downcase}_fields"
  end

  private

  def deal_fields
    field_response_hash = get_field_response(URI("#{API_URL}/#{API_VERSION}/deals/fields"))
    return field_response_hash unless field_response_hash[:success]

    field_response_hash[:data].reject! { |field| DEAL_EXCLUDED_VARIABLES.include? field['name'] }
    field_response_hash
  end

  def lead_fields
    get_field_response(URI("#{API_URL}/#{API_VERSION}/entities/lead/fields"))
  end

  def contact_fields
    get_field_response(URI("#{API_URL}/#{API_VERSION}/entities/contact/fields"))
  end

  def meeting_fields
    url = URI("#{API_URL}/#{API_VERSION}/meetings/fields")

    field_response_hash = get_field_response(url)
    field_response_hash[:data].reject! { |field| MEETING_EXCLUDED_VARIABLES.include? field['name'] }
    field_response_hash
  end

  def call_log_fields
    field_response_hash = get_field_response(URI("#{API_URL}/#{API_VERSION}/call-logs/fields"))
    field_response_hash[:data].reject! { |field| CALL_LOG_EXCLUDED_VARIABLES.include? field['name'] }
    field_response_hash
  end

  def get_field_response(url)
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Get.new(url)
    request['api-key'] = @current_tenant.kylas_api_key
    begin
      response = https.request(request)
      response_body = JSON.parse(response.body)
      return { success: true, data: response_body } if response.code == '200'

      {
        success: false,
        error: response_body['message'].blank? ? I18n.t('fetch_entity_fields.failure_message') : response_body['message']
      }
    rescue StandardError => e
      Rails.logger.error "Kylas::FetchEntityFieldService: get_field_response: Errors -> #{e.message}"
      { success: false, error: I18n.t('fetch_entity_fields.failed_to_get_field') }
    end
  end
end
