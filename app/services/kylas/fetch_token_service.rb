# frozen_string_literal: true

class Kylas::FetchTokenService
  def initialize(user)
    @user = user
  end

  def get_token
    return { success: false } unless @user.present? && @user.kylas_refresh_token.present?

    url = URI(KYLAS_OAUTH_URL)
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Post.new(url)
    request.basic_auth(Rails.application.credentials.kylas.client_id, Rails.application.credentials.kylas.client_secret)
    request.body = URI.encode_www_form(
      [
        ['grant_type', 'refresh_token'],
        ['refresh_token', @user.kylas_refresh_token]
      ]
    )
    begin
      response = https.request(request)
      return { success: false } unless response.code == '200'
      parsed_response = JSON.parse(response.body)
      {
        success: true,
        value: {
          kylas_access_token: parsed_response['access_token'],
          kylas_refresh_token: parsed_response['refresh_token'],
          kylas_access_token_expires_at: parsed_response['expires_in'].seconds.from_now
        }
      }
    rescue StandardError => e
      Rails.logger.error "Kylas::FetchTokenService: get_token: Errors -> #{e.message}"
      { success: false }
    end
  end
end
