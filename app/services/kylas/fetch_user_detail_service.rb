# frozen_string_literal: true

class Kylas::FetchUserDetailService
  def initialize(user)
    @user = user
  end

  def get_details
    process(URI(KYLAS_ME_URL))
  end

  def get_another_users_details(kylas_user_id)
    process(URI("#{KYLAS_USERS_URL}/#{kylas_user_id}"))
  end

  private

  def fetch_errors(parsed_resp)
    error_message = I18n.t('fetch_user_detail_service.failure_message')
    error_message += "Error Code: #{parsed_resp['code']} " if parsed_resp['code'].present?
    error_message += "Error Description: #{parsed_resp['message']}" if parsed_resp['message'].present?
    error_message
  end

  def process(url)
    kylas_access_token = @user.get_kylas_access_token
    return { success: false } if kylas_access_token.blank?

    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Get.new(url)
    request["Authorization"] = "Bearer #{kylas_access_token}"
    begin
      response = https.request(request)
      return { success: false, error: fetch_errors(JSON.parse(response.body)) } unless response.code == '200'
      { success: true, details: JSON.parse(response.body).with_indifferent_access }
    rescue StandardError => e
      Rails.logger.error "Kylas::FetchUserDetailService: get_details: Errors -> #{e.message}"
      { success: false, error: e.message }
    end
  end
end
