# frozen_string_literal: true

class Kylas::FetchUsersService
  def initialize user
    @user = user
    @size = 1000
    @page = -1
  end

  def fetch_users
    return { success: false } if @user.get_kylas_access_token.blank?

    code, parsed_body = process
    if code == '200'
      users = parse_user_response(parsed_body)
      number_of_pages = parsed_body['totalPages'].to_i
      if number_of_pages > 1
        (number_of_pages - 1).times do
          code, parsed_body = process
          users += parse_user_response(parsed_body) if code == '200'
        end
      end

      { success: true, data: users }
    else
      { success: false }
    end
  end

  private

  def process
    url, body     = request_payload
    https         = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request       = Net::HTTP::Post.new(url)
    request.body  = JSON.dump(body)
    request["Authorization"] = "Bearer #{@user.get_kylas_access_token}"
    request["Content-Type"]  = "application/json"
    response                 =  https.request(request)
    [response.code, JSON.parse(response.body)]
  end

  def request_payload
    [
      URI("#{KYLAS_USERS_URL}/search?sort=createdAt,asc&page=#{page}&size=#{@size}"),
      {
        fields: %w[firstName lastName email id],
        jsonRule: {
          "rules": [
            {
              "operator": "equal",
              "id": "active",
              "field": "active",
              "type": "boolean",
              "value": true
            }
          ],
          "condition": "AND",
          "valid": true
        }
      }
    ]
  end

  def page
    @page += 1
  end

  def parse_user_response(parsed_body)
    parsed_body.try(:[], 'content')
  end
end
