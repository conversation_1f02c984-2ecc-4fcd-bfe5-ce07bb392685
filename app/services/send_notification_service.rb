class SendNotificationService
  def initialize(params)
    @params = params
  end

  def process
    if @params['api_key'].blank?
      Rails.logger.error "API key is blank"
      return
    end

    @tenant = KylasEngine::Tenant.find_by('webhook_api_key': @params['api_key'])
    if @tenant.blank?
      Rails.logger.error "API key is not valid #{@params['api_key']}"
      return
    end

    if @tenant.connected_account.blank? || @tenant.connected_account&.status == INACTIVE
      Rails.logger.error "Tenant has not connected an account or account is inactive #{@tenant.id}"
      return
    end

    if @params['entityId'].blank? || @params['resourceId'].blank?
      Rails.logger.error "Entity id or resource id is missing in payload entity id: #{@params[:entityId]}, resource id: #{@params[:resourceId]}"
      return
    end

    @workflow_action = @tenant.workflow_actions.find_by_id(@params['resourceId'])
    if @workflow_action.blank?
      Rails.logger.error "Workflow action is not present for resourceId: #{@params['resourceId']}"
      return
    end

    Teams::SendNotification.new(@tenant, @workflow_action, @params).process
  end
end
