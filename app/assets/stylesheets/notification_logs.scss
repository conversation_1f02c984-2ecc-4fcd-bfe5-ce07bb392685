.popover{
  width: 25rem;
  max-width: 30rem;
}

.popover-content { white-space: pre-wrap; }

.entity-table{
  .entity-details{
    display: flex;
    flex-direction: column;
  }
  a { text-decoration: none; }

  .entity-details {
    .entity-name{
      color: #687790;
      span{ color: black; }
    }
  }
  .log-status{
    display: flex;
    flex-direction: column;
    .error-message{ color: red }
  }
  .notified-at{ color: #687790; }
}

.bottom-bar{
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row;

  .pagination{ margin-bottom: 0; }
}

