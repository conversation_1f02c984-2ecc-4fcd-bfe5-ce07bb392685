/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS (and SCSS, if configured) file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */
@import "kylas_engine/application";
@import "navbar";
@import "notification_template";
@import "notification_logs";
@import "agent_mappings";

.dashboard-layout{
  &__bucket{
    margin: 1rem;
    z-index: auto;
  }
}

@-moz-document url-prefix() {
  .dashboard-layout__bucket { margin-top: 1rem; }
}

.table{
  table-layout: fixed;
  word-break: break-all;
}

.table thead { background-color: #E7F0FD; }

.top-bar {
  padding: 1vw;
  padding-top: 0vw;
}

.bottom-bar {
  padding: 1vw;
  background-color: white;
}

.app-name {
  font-size: 18px;
  padding-bottom: 1%;
}

.secondary-label { color: #6B778E; }

.cursor-pointer { cursor: pointer; }

.required:after {
  content: "*";
  margin-left: 2px;
  color: red;
}

.checkbox-custom-style {
  width: 1rem;
  height: 1rem;
  border-radius: 0px !important;
  vertical-align: text-bottom;
  &:focus {
    box-shadow: none;
  }
}

.table-responsive { overflow-x: hidden; }
.dropdown-toggle::after { display: inline-block; }

.select2-selection--single, .select2-selection__arrow{ height: 38px !important; }
.select2-selection__rendered{ line-height: 38px !important; }
