# frozen_string_literal: true

class ConnectedAccount < ApplicationRecord
  # Encryption
  encrypts :access_token, :refresh_token, deterministic: true

  belongs_to :tenant, class_name: 'KylasEngine::Tenant'
  belongs_to :user, class_name: 'KylasEngine::User'
  has_many   :notification_templates, dependent: :destroy
  has_many   :agent_mappings, dependent: :destroy

  validates :access_token, :refresh_token, :microsoft_user_id, presence: true
  validates :status, inclusion: {in: [ACTIVE, INACTIVE], message: "%{value} is not a valid status"}
  validates :account_type, inclusion: {in: [TEAMS], message: "%{value} is not a valid account type"}

  def get_access_token
    return self.access_token if self.expires_at.to_i >= Time.now.to_i
    token_response = Teams::FetchNewTokenService.new(self).get_token
    return nil unless token_response[:success]
    self.update(token_response[:value]) ? token_response[:value][:access_token] : nil
  end
end
