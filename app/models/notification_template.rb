# frozen_string_literal: true

class NotificationTemplate < ApplicationRecord

  belongs_to :connected_account
  belongs_to :tenant, class_name: '<PERSON>ylasEngine::Tenant'
  has_many :workflow_actions, dependent: :destroy
  has_many :notifiers, dependent: :destroy
  has_many :variable_type_mappings, dependent: :destroy

  accepts_nested_attributes_for :notifiers
  accepts_nested_attributes_for :variable_type_mappings

  validates :title, :heading, :content, presence: true
  validates :message_type, inclusion: { in: MESSAGE_PRIORITY_MAPPING.keys.map(&:to_s), message: "%{value} is not a valid message type" }

  attr_accessor :clone
end
