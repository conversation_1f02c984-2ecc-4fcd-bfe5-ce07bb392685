# frozen_string_literal: true

class WorkflowAction < ApplicationRecord

  belongs_to :notification_template
  belongs_to :user,   class_name: 'KylasEngine::User'
  belongs_to :tenant, class_name: 'KylasEngine::Tenant'

  validates :entity_type, presence: true, inclusion: {in: ALLOWED_ENTITIES, message: "%{value} is not a valid entity type"}

  accepts_nested_attributes_for :notification_template
end
