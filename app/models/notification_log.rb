# frozen_string_literal: true

class NotificationLog < ApplicationRecord
  belongs_to :tenant, class_name: 'KylasEngine::Tenant'

  validates :entity_type, presence: true, inclusion: {in: ALLOWED_ENTITIES, message: "%{value} is not a valid entity type"}
  validates :status, presence: true, inclusion: {in: [SUCCESS, FAILED], message: "%{value} is not a valid status"}

  def entity_url(entity_id)
    case self.entity_type
    when DEAL, LEAD, CONTACT
      "#{WEB_APP_URL}/sales/#{self.entity_type.downcase.pluralize}/details/#{entity_id}"
    when CALL_LOG
      "#{WEB_APP_URL}/sales/calls/list?id=#{entity_id}"
    when MEETING
      "#{WEB_APP_URL}/sales/meetings/list?id=#{entity_id}"
    else
      "#"
    end
  end
end
