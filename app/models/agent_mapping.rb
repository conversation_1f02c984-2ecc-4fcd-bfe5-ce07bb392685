# frozen_string_literal: true

class AgentMapping < ApplicationRecord
  belongs_to :tenant, class_name: 'KylasEngine::Tenant'
  belongs_to :user, class_name: 'KylasEngine::User'
  belongs_to :connected_account

  validates :name, :identifier, presence: true
  validates :identifier_type, inclusion: { in: [TEAMS], message: "%{value} is not a valid type" }
  validates :identifier, uniqueness: { scope: [:tenant_id, :identifier_type], message: "mapping already present" }
  validates :user_id, uniqueness: { scope: [:tenant_id, :identifier_type], message: "mapping already present" }
end
