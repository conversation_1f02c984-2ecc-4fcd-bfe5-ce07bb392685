# frozen_string_literal: true

class Notifier < ApplicationRecord
  belongs_to :notification_template

  validates :recipient_type, inclusion: { in: [CHANNEL, USER, RELATIVE], message: "%{value} is not a valid recipient type" }

  def get_team_or_chat_id(options = {})
    @options = options
    send("get_#{recipient_type}_chat_id")
  end

  private

  def get_channel_chat_id
    team_or_chat_id
  end

  def get_user_chat_id
    return team_or_chat_id unless team_or_chat_id.nil?

    chat_id_response = Teams::FetchUsersService.new(notification_template.connected_account).get_chat_id(recipient_id)
    return nil unless chat_id_response[:success]

    self.update_column(:team_or_chat_id, chat_id_response[:chat_id])
    chat_id_response[:chat_id]
  end

  def get_relative_chat_id
    kylas_user = KylasEngine::User.find_by(tenant_id: @options[:tenant_id], kylas_user_id: @options[recipient_id.to_sym])
    return nil if kylas_user.nil?

    agent_mapping = AgentMapping.find_by(tenant_id: @options[:tenant_id], user_id: kylas_user.id, identifier_type: notification_template.connected_account.account_type)
    return nil if agent_mapping.nil?

    chat_id_response = Teams::FetchUsersService.new(notification_template.connected_account).get_chat_id(agent_mapping.identifier)
    return chat_id_response[:chat_id]
  end
end
