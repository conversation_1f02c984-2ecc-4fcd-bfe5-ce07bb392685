require 'sidekiq/web'

Rails.application.routes.draw do
  mount KylasEngine::Engine, at: '/'
  mount Sidekiq::Web => '/sidekiq'
  mount Ckeditor::Engine => '/ckeditor'

  if Rails.env.production? || Rails.env.staging? || Rails.env.selldo_production? || Rails.env.selldo_staging?
    Sidekiq::Web.use Rack::Auth::Basic do |username, password|
      username == Rails.application.credentials.sidekiq[:username] && password == Rails.application.credentials.sidekiq[:password]
    end
  end

  authenticated :user do
    root to: 'kylas_engine/dashboards#help', as: :authenticated_root
  end

  devise_scope :user do
    root 'devise/sessions#new'
  end

  resources :connected_accounts, path: 'connected-account', only: [:index] do
    collection do
      post :disconnect
      get :connect, to: 'connected_accounts#get_access_token'
    end

    resources :agent_mappings, path: 'agent-mappings', only: [:index, :create, :new, :edit, :update, :destroy] do
      collection do
        post :sync
        get :autocomplete
      end
    end
  end

  resources :manage_users, path: 'manage-users', only: [:index, :edit, :update] do
    collection do
      post :sync
    end
  end

  resources :workflow_actions, path: 'workflow-actions', only: [] do
    collection do
      get 'fetch-or-build', to: "workflow_actions#fetch_or_build"
      post 'save', to: "workflow_actions#save"
      post ':api_key/deliver-via-workflow', to: 'workflow_actions#deliver_via_workflow'
    end
  end

  resources :notification_templates, path: 'notification-templates', only: [:index, :new, :edit, :create, :update] do
    collection do
      get ':entity_type/fetch-variables', to: 'notification_templates#fetch_variables'
      post 'fetch-content', to: 'notification_templates#fetch_content'
      get 'fetch-teams-users', to: 'notification_templates#fetch_teams_users_by_name'
      get 'fetch-mention-users', to: 'notification_templates#fetch_mention_users'
    end

    member do
      get :clone
      get 'fetch-notify-options', to: 'notification_templates#fetch_notify_options'
    end
  end

  get '/notification-logs', to: 'notification_logs#index'
end
