#frozen_string_literal: true

if Rails.env.selldo_production? || Rails.env.selldo_staging?
  PRODUCT_NAME = "Sell Do"
  APP_NAME = "Sell Do Notifier"
  LOGO_URL = "https://selldo-assets.sgp1.digitaloceanspaces.com/images/blue-logo.png"
  SUPPORT_URL = "https://support.sell.do"
  PRIVACY_URL = "https://www.sell.do/privacy-policy"
  TERMS_URL = "https://www.sell.do/terms-of-service"
  CONTACT_URL = "https://www.sell.do/contact-us"
else
  PRODUCT_NAME = "Kylas"
  APP_NAME = "Kylas Notifier"
  LOGO_URL = "https://assets.kylas.io/images/logo.png"
  SUPPORT_URL = "https://support.kylas.io"
  PRIVACY_URL = "https://www.kylas.io/privacy"
  TERMS_URL = "https://www.kylas.io/terms-of-service"
  CONTACT_URL = "https://www.kylas.io/contact-us"
end

if Rails.env.staging? || Rails.env.production? || Rails.env.selldo_staging? || Rails.env.selldo_production?
  LOGDNA = Logdna::Ruby.new(
    Rails.application.credentials.log_dna_ingestion_key,
    {
      app: "#{PRODUCT_NAME.downcase.delete(' ')}-notification-#{Rails.env}",
      env: Rails.env
    }
  )
  Rails.logger = LOGDNA
end

if Rails.env.selldo_production?
  API_URL = 'https://api.sell.do'
  WEB_APP_URL = 'https://crm.sell.do'
elsif Rails.env.selldo_staging?
  API_URL = 'https://api-qa.sell-do.com'
  WEB_APP_URL = 'https://app-qa.sell-do.com'
elsif Rails.env.production?
  API_URL = 'https://api.kylas.io'
  WEB_APP_URL = 'https://app.kylas.io'
elsif Rails.env.staging?
  API_URL = 'https://api-qa.sling-dev.com'
  WEB_APP_URL = 'https://app-qa.sling-dev.com'
else
  API_URL = 'https://api-qa.sling-dev.com'
  WEB_APP_URL = 'https://app-qa.sling-dev.com'
end

API_VERSION = 'v1'
MICOROSOFT_API_VERSION_1 = 'v1.0'
MICOROSOFT_API_VERSION_2 = 'v2.0'

MICROSOFT_AUTH_SCOPE = 'https://graph.microsoft.com/.default offline_access email openid profile'
MICROSOFT_TOKEN_URL = "https://login.microsoftonline.com/common/oauth2/#{MICOROSOFT_API_VERSION_2}/token"
MICROSOFT_AUTH_URI = "https://login.microsoftonline.com/common/oauth2/#{MICOROSOFT_API_VERSION_2}/authorize?client_id=#{Rails.application.credentials.notifier_app.client_id}&response_type=code&response_mode=query&redirect_uri=#{Rails.application.credentials.notifier_app.redirect_url}&scope=#{CGI.escape(MICROSOFT_AUTH_SCOPE)}&prompt=consent"
MICROSOFT_FETCH_TEAMS_URI = "https://graph.microsoft.com/#{MICOROSOFT_API_VERSION_1}/me/joinedTeams"
MICROSOFT_CHANNEL_BASE_URI = "https://graph.microsoft.com/#{MICOROSOFT_API_VERSION_1}/teams"
MICROSOFT_FETCH_USERS_URI = "https://graph.microsoft.com/#{MICOROSOFT_API_VERSION_1}/users"
MICROSOFT_CHAT_URI = "https://graph.microsoft.com/#{MICOROSOFT_API_VERSION_1}/chats"

KYLAS_OAUTH_URL = "#{API_URL}/oauth/token"
KYLAS_ME_URL = "#{API_URL}/v1/users/me"
KYLAS_USERS_URL = "#{API_URL}/v1/users"

DEAL = 'DEAL'
LEAD = 'LEAD'
CONTACT = 'CONTACT'
CHANNEL = 'channel'
USER = 'user'
RELATIVE = 'relative'
WORKFLOW = 'workflow'
MOBILE = 'MOBILE'
NAME = 'name'
SUCCESS = 'SUCCESS'
FAILED = 'FAILED'
ACTIVE = 'ACTIVE'
INACTIVE = 'INACTIVE'
CALL_LOG = 'CALL_LOG'
MEETING = 'MEETING'
ALLOWED_ENTITIES = [DEAL, LEAD, CONTACT, CALL_LOG, MEETING]

DEAL_EXCLUDED_VARIABLES = ['quantity', 'price', 'discount', 'units']
MEETING_EXCLUDED_VARIABLES = ['checkedInLatitude', 'checkedInLongitude', 'checkedOutLatitude', 'checkedOutLongitude']
CALL_LOG_EXCLUDED_VARIABLES = ['notes', 'associatedLeads', 'associatedContacts', 'associatedDeals', 'ivrNumber', 'phoneNumber', 'entityType']

TEAMS = 'TEAMS'

MESSAGE_PRIORITY_MAPPING = {
  "normal": "Standard",
  "high": "Important",
  "urgent": "Urgent"
}.freeze


SEPERATOR = '|*|'
RELATIVE_OPTIONS = {
  'record_owner': 'Record Owner',
  'record_created_by': 'Record Created By',
  'record_updated_by': 'Record Updated By'
}.freeze
