# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t "hello"
#
# In views, this is aliased to just `t`:
#
#     <%= t("hello") %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   "true": "foo"
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  app_name: "Kylas Notification"
  privacy_policy: "Privacy Policy"
  support: "Support"
  contact_us: "Contact Us"
  keep_me_logged_in: "Keep me logged in"
  name: "Name"
  edit: "Edit"
  cancel: 'Cancel'
  save: 'Save'
  active: 'Active'
  select_user_name: 'Select user name'
  title: 'Title'
  password: "Password"
  email_registration: "has been registered with us. Please verify your account by clicking on the link below"
  verify: "Verify"
  your_email: "Your email"
  sign_in: "Sign in to your account"
  sign_up: "Sign up to your account"
  sign_up_text: "By clicking on 'Sign up for free' you agree to our"
  terms: "terms"
  yes_label: "Yes"
  no_label: "No"
  saving: 'Saving'
  term_acknowledge_text: "and you acknowledge having read our"
  existing_account: "Having an existing account."
  sign_in_desktop: "For better user experience, sign in from a desktop/laptop browser."
  dont_have_account: "Don't have an account?"
  failure_and_contact_message: "Something went wrong. Please try again later or contact %{product_name} support"
  account_not_connected: "Account not connected, Please connect your account first"
  please_wait: "Please Wait..."
  view_message: "View Message"
  notification: "Notification"
  sign_in_message: "Please sign in first before continue"
  add_api_key: 'Please add api key to perform this action'
  reconnect: 'Reconnect'
  user_id: 'User Id'
  failure_message: 'Something went wrong.'
  devise:
    mailer:
      confirmation_instructions:
        subject: '%{app_name}: Confirmation instructions'
      reset_password_instructions:
        subject: "%{app_name}: Reset password instructions"
      unlock_instructions:
        subject: "%{app_name}: Unlock instructions"
      email_changed:
        subject: "%{app_name}: Email Changed"
      password_change:
        subject: "%{app_name}: Password Changed"
  connected_accounts:
    title: "Connected Accounts"
    no_account_connected: "No Account Connected!"
    connect_account: "Connect Account"
    template_name: 'Template Name'
    account_connection_text: "You have only one connected account at a point, by connecting this account your current connected account will be disconnected."
    account_connection_confirmation: "Are you sure, you want to proceed?"
    account_disconnection_text: "You won't be able to add/edit templates and be unable to send notifications after disconnection. Are you sure, you want to continue?"
    yes_disconnect: "Yes, Disconnect"
    name: "Name"
    email: "Email"
    status: "Status"
    actions: "Actions"
    disconnect: "Disconnect"
    connect: 'Connect'
    successful: "Account Connected Successfully"
    account_disconnected_successfully: "Account disconnected successfully"
    failed_to_disconnect: "Failed to disconnect account"
    failed_to_connect: 'Failed to connect account, please try again later.'
    account_not_exist: 'Account not exist, Invalid request.'
    login_failed: "Unable to login!"
    login_successful: "Login successful!"
    account_already_exist: "Account already exist, Please disconnect existing account first."
    email_mismatched: 'Email mismatched, Please login with same email address'
    account_already_inactive: 'Account is already inactive'
    account_inactive: 'All connected accounts either in-active or no account found. Please add/active at least one account.'
  tenants:
    kylas_api_key_navbar_placeholder: "API Keys"
    api_key: '%{app_name} API Key'
    api_key_help: '(You can use this API Key for your Webhooks in %{app_name})'
    copy_value: '%{element} copied!'
    generate_webhook_api_key: 'We generate webhook api key which can be used for your webhooks in %{app_name}. Please add %{app_name} API Key.'
  workflow_actions:
    user_id_missing: "User ID is missing"
    tenant_id_missing: "Tenant ID is missing"
    entity_type_missing: "Entity Type is missing"
    identifier_missing: "Identifier is missing"
    not_found: "Workflow action is not found, please create another marketplace action"
  fetch_entity_fields:
    failure_message: "Something went wrong!"
    failed_to_get_field: "Failed to fetch field, please try again later"
    invalid_entity_type: "Invalid entity type"
  notification_templates:
    title: 'Notification Templates'
    preview: 'Preview'
    notify_to: "Notify To"
    select: "Select"
    heading": "Heading"
    insert: "Insert"
    entity_type: "Entity Type"
    insert_variable: "Insert Variable"
    add_title: "Add Title"
    add_heading: "Add Heading"
    body: "Body"
    saved_successfully: "Notification template saved successfully"
    failed_to_save: 'Failed to save notification template.'
    not_found: "Notification Templates not found!"
    attach_view_cta: "Attach view Entity CTA"
    error_message: "Please connect with the App Developer or %{app_name} Support, If you faced any issues."
    cta_tooltip: "When selected, it will attach 'View Entity' CTA in notification that will redirect to the created"
    add_template: 'Add Notification Template'
    notification_title: 'Notification Title'
    recipient_name: 'Recipient Name'
    account_name: 'Account Name'
    created_at: 'Created At'
    actions: "Actions"
    template_id_missing: "Template Id is missing"
    heading_missing: 'Template heading is missing'
    content_missing: 'Template content is missing'
    entity_type_missing: 'Template entity type is missing'
    notifiers_missing: 'Notifiers are missing'
    active: 'Active'
    select_template: 'Select Template'
    workflow_missing: 'Workflow not found'
    heading_or_content_missing: 'Heading or content missing'
    active_templates_not_found: 'No active notification template found for: Entity %{entity_type}'
    entity_fields_missing: 'Entity Fields are missing'
    unable_to_fetch_entity_fields: 'Unable to fetch entity fields, Please try again later!'
    connect_account_request: 'Account not connected or account is inactive, Please connect your account first'
    query_parameter_missing: 'Failed to fetch users, query parameters is missing.'
    default_notification_message: 'Sent by Kylas Notifier'
    message_type: 'Select Message Type'
    standard_message_helper: 'Message will be sent as usual'
    important_message_helper: 'Message will be marked as important'
    urgent_message_helper: 'Recipient will be notified every 2 min for next 20 min'
    clone: 'Clone'
    edit_template_warning: 'You need to save the workflow where this template is used in Kylas again in order to reflect the changes.'
    mentions: 'Mentions'
  manage_users:
    title: 'Manage Users'
    user_not_added: 'No User Added!'
    fetch_and_map_users: 'Fetch And Map Users'
    name: 'Name'
    confirmed: 'Confirmed'
    actions: 'Actions'
    confirm: 'Confirm'
    user_fetch_in_progress: 'User fetching is already in progress please check after some time'
    fetching_scheduled: 'Fetching is in progress. Please refresh the page after sometime.'
    fetching: "Fetching..."
    invalid_user_provided: 'Invalid user provided'
    success: 'User %{action} successfully'
    failure: 'Failed to %{action} user'
    app_user_name: '%{product_name} User Name'
    user_name: 'User Name'
    select_user: 'Select User'
    user_email: 'User Email'
    select_user: 'Select User'
  microsoft_service:
    account_not_exist: 'Account not connected'
    channels_fetch_failed: 'Failed to fetch channels, Please try again later.'
  notification_logs:
    title: "Notification Logs"
    entity_details: 'Entity Details'
    recipient_name: 'Recipient Name'
    status: 'Status'
    notified_at: "Notified At"
    actions: 'Actions'
    not_found: 'Notification Logs not found!'
  fetch_user_detail_service:
    failure_message: 'Failed to get user details, please try again later '
  agent_mapping:
    title: 'All Agents'
    view_mappings: 'View Mappings'
    insufficient_params_passed: 'Insufficient parameters passed'
    syncing: 'Syncing...'
    map_users: 'Map Users'
    agent_not_found: 'No Agent Found!'
    add_agent: 'Add Agent'
    success: 'Agent %{action} successfully'
    failure: 'Failed to %{action} agent mapping'
    sync_help_text: 'We will fetch and create the new mapping if user added to your account'
    select_user: 'Select %{type} User'
    id: 'Id'
    actions: 'Actions'
    name: 'Name'
    sync: 'Sync'
    invalid_mapping_id: 'Invalid mapping id passed'
    add_mapping: 'Add Mapping'
    type_id: '%{type} Id'
    type_name: '%{type} Name'
    account_not_connected: 'Account not connected. Please connect your account first'
    delete_mapping_message: 'Are you sure to delete this mapping?'
    yes_delete: 'Yes, delete'
    mapping_in_progress: 'Agent mapping is already in progress'
    schedule_sync: 'Mapping sync scheduled. Please refresh this page after some time'
