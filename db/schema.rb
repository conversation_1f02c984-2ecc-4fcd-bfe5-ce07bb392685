# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2024_02_26_100417) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "agent_mappings", force: :cascade do |t|
    t.string "name"
    t.string "identifier"
    t.string "identifier_type"
    t.bigint "connected_account_id", null: false
    t.bigint "tenant_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["connected_account_id"], name: "index_agent_mappings_on_connected_account_id"
    t.index ["tenant_id"], name: "index_agent_mappings_on_tenant_id"
    t.index ["user_id"], name: "index_agent_mappings_on_user_id"
  end

  create_table "connected_accounts", force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.bigint "tenant_id", null: false
    t.bigint "user_id", null: false
    t.string "access_token"
    t.string "refresh_token"
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "status", default: "ACTIVE"
    t.string "microsoft_user_id"
    t.string "account_type"
    t.index ["tenant_id"], name: "index_connected_accounts_on_tenant_id"
    t.index ["user_id"], name: "index_connected_accounts_on_user_id"
  end

  create_table "kylas_engine_tenants", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "kylas_api_key"
    t.string "webhook_api_key"
    t.bigint "kylas_tenant_id"
    t.string "timezone", default: "Asia/Calcutta"
    t.string "fetch_and_create_users_job"
    t.string "agent_mapping_job"
  end

  create_table "kylas_engine_users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.boolean "is_tenant", default: false
    t.string "kylas_access_token"
    t.string "kylas_refresh_token"
    t.datetime "kylas_access_token_expires_at"
    t.boolean "active", default: false
    t.bigint "tenant_id"
    t.bigint "kylas_user_id"
    t.index ["confirmation_token"], name: "index_kylas_engine_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_kylas_engine_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_kylas_engine_users_on_reset_password_token", unique: true
    t.index ["tenant_id"], name: "index_kylas_engine_users_on_tenant_id"
  end

  create_table "notification_logs", force: :cascade do |t|
    t.bigint "entity_id"
    t.string "entity_type"
    t.string "status"
    t.string "recipient_name"
    t.string "error_message"
    t.string "account_name"
    t.string "template_name"
    t.string "notification_content"
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_notification_logs_on_tenant_id"
  end

  create_table "notification_templates", force: :cascade do |t|
    t.string "title"
    t.string "heading"
    t.text "content"
    t.boolean "view_entity_cta", default: false
    t.bigint "connected_account_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "entity_type"
    t.boolean "active", default: true
    t.bigint "tenant_id"
    t.string "message_type"
    t.index ["connected_account_id"], name: "index_notification_templates_on_connected_account_id"
    t.index ["tenant_id"], name: "index_notification_templates_on_tenant_id"
  end

  create_table "notifiers", force: :cascade do |t|
    t.string "recipient_type"
    t.string "recipient_id"
    t.string "recipient_name"
    t.string "team_or_chat_id"
    t.bigint "notification_template_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notification_template_id"], name: "index_notifiers_on_notification_template_id"
  end

  create_table "variable_type_mappings", force: :cascade do |t|
    t.string "name"
    t.string "variable_type"
    t.bigint "notification_template_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notification_template_id"], name: "index_variable_type_mappings_on_notification_template_id"
  end

  create_table "workflow_actions", force: :cascade do |t|
    t.string "entity_type"
    t.bigint "notification_template_id", null: false
    t.bigint "tenant_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notification_template_id"], name: "index_workflow_actions_on_notification_template_id"
    t.index ["tenant_id"], name: "index_workflow_actions_on_tenant_id"
    t.index ["user_id"], name: "index_workflow_actions_on_user_id"
  end

  add_foreign_key "agent_mappings", "connected_accounts"
  add_foreign_key "agent_mappings", "kylas_engine_tenants", column: "tenant_id"
  add_foreign_key "agent_mappings", "kylas_engine_users", column: "user_id"
  add_foreign_key "connected_accounts", "kylas_engine_tenants", column: "tenant_id"
  add_foreign_key "connected_accounts", "kylas_engine_users", column: "user_id"
  add_foreign_key "kylas_engine_users", "kylas_engine_tenants", column: "tenant_id"
  add_foreign_key "notification_logs", "kylas_engine_tenants", column: "tenant_id"
  add_foreign_key "notification_templates", "connected_accounts"
  add_foreign_key "notifiers", "notification_templates"
  add_foreign_key "variable_type_mappings", "notification_templates"
  add_foreign_key "workflow_actions", "kylas_engine_tenants", column: "tenant_id"
  add_foreign_key "workflow_actions", "kylas_engine_users", column: "user_id"
  add_foreign_key "workflow_actions", "notification_templates"
end
