class CreateNotificationTemplates < ActiveRecord::Migration[7.0]
  def change
    create_table :notification_templates do |t|
      t.string      :recipient_id
      t.string      :recipient_type
      t.string      :team_id
      t.string      :title
      t.string      :heading
      t.text        :content
      t.boolean     :view_entity_cta,   default: false
      t.references  :connected_account, null: false, foreign_key: { to_table: :connected_accounts }, index: true
      t.timestamps
    end
  end
end
