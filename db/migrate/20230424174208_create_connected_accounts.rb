class CreateConnectedAccounts < ActiveRecord::Migration[7.0]
  def change
    create_table :connected_accounts do |t|
      t.string        :name
      t.string        :email
      t.references    :tenant,  null: false, foreign_key: { to_table: :kylas_engine_tenants }, index: true
      t.references    :user,    null: false, foreign_key: { to_table: :kylas_engine_users },   index: true
      t.string        :access_token
      t.string        :refresh_token
      t.datetime      :expires_at
      t.timestamps
    end
  end
end
