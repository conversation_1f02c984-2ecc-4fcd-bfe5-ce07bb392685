class CreateNotificationLogs < ActiveRecord::Migration[7.0]
  def change
    create_table :notification_logs do |t|
      t.bigint      :entity_id
      t.string      :entity_type
      t.string      :status
      t.string      :recipient_name
      t.string      :error_message
      t.string      :account_name
      t.string      :template_name
      t.string      :notification_content
      t.references  :tenant,  null: false, foreign_key: { to_table: :kylas_engine_tenants }, index: true
      t.timestamps
    end
  end
end
