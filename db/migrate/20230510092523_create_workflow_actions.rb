class CreateWorkflowActions < ActiveRecord::Migration[7.0]
  def change
    create_table :workflow_actions do |t|
      t.string        :entity_type
      t.references    :notification_template, null: false, foreign_key: { to_table: :notification_templates },   index: true
      t.references    :tenant, null: false, foreign_key: { to_table: :kylas_engine_tenants },   index: true
      t.references    :user, null: false, foreign_key: { to_table: :kylas_engine_users },   index: true
      t.timestamps
    end
  end
end
