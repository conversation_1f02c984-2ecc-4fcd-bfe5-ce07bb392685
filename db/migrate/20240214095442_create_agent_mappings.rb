class CreateAgentMappings < ActiveRecord::Migration[7.0]
  def change
    create_table :agent_mappings do |t|
      t.string     :name
      t.string     :identifier
      t.string     :identifier_type
      t.references :connected_account, null: false, foreign_key: { to_table: :connected_accounts }
      t.references :tenant, null: false, foreign_key: { to_table: :kylas_engine_tenants }
      t.references :user, null: false, foreign_key: { to_table: :kylas_engine_users }
      t.timestamps
    end
  end
end
